/**
 * Integração do Globo Esporte Scraper com as Landing Pages
 * Este arquivo mostra como usar o scraper para atualizar os dados das páginas
 */

class LandingPageIntegration {
  constructor() {
    this.scraper = new GloboEsporteScraper();
    this.updateInterval = 5 * 60 * 1000; // 5 minutos
    this.intervalId = null;
  }

  /**
   * Inicializa a integração automática
   */
  async init() {
    console.log('🔄 Inicializando integração com Globo Esporte...');
    
    // Primeira atualização
    await this.updatePageData();
    
    // Configura atualizações automáticas
    this.startAutoUpdate();
    
    // Listener para atualização manual
    this.setupManualUpdate();
  }
  /**
   * Atualiza os dados da página
   */
  async updatePageData() {
    try {
      const data = await this.scraper.getData();
      
      // Debug: mostra os dados recebidos
      console.log('📊 Dados recebidos do scraper:', {
        artilheiros: data.artilheiros?.map(a => `${a.name}: ${a.goals} gols`),
        classificacao: data.classificacao?.length,
        jogos: data.jogos?.length,
        source: data.source || 'API'
      });
      
      // Atualiza artilheiros
      this.updateTopScorers(data.artilheiros);
      
      // Atualiza classificação
      this.updateStandings(data.classificacao);
      
      // Atualiza jogos
      this.updateFixtures(data.jogos);
      
      // Mostra indicador de atualização
      this.showUpdateIndicator(data.timestamp);
      
      console.log('✅ Dados da página atualizados com sucesso');
      
    } catch (error) {
      console.error('❌ Erro ao atualizar dados da página:', error);
      this.showErrorIndicator();
    }
  }  /**
   * Valida e corrige dados dos artilheiros
   */
  validateAndFixScorers(scorers) {
    if (!Array.isArray(scorers)) {
      console.warn('⚠️ Dados de artilheiros inválidos, usando fallback');
      return this.getDefaultScorers();
    }

    const validScorers = scorers
      .filter(scorer => scorer && scorer.name && typeof scorer.goals === 'number')
      .map(scorer => ({
        name: String(scorer.name).trim(),
        team: String(scorer.team || 'N/A').trim(),
        goals: Number(scorer.goals) || 0,
        image: scorer.image || scorer.logo || 'https://via.placeholder.com/120x120/E9C043/000000?text=Player',
        position: scorer.position || 0
      }));

    if (validScorers.length === 0) {
      console.warn('⚠️ Nenhum artilheiro válido encontrado, usando fallback');
      return this.getDefaultScorers();
    }

    return validScorers;
  }

  /**
   * Dados padrão dos artilheiros
   */
  getDefaultScorers() {
    return [
      { name: 'Erling Haaland', team: 'Manchester City', goals: 8, image: 'https://cdn.sofifa.net/players/239/085/25_120.png', position: 1 },
      { name: 'Kylian Mbappé', team: 'Real Madrid', goals: 7, image: 'https://cdn.sofifa.net/players/231/747/25_120.png', position: 2 },
      { name: 'Vinicius Jr', team: 'Real Madrid', goals: 6, image: 'https://cdn.sofifa.net/players/238/794/25_120.png', position: 3 },
      { name: 'Lionel Messi', team: 'Inter Miami', goals: 5, image: 'https://cdn.sofifa.net/players/158/023/25_120.png', position: 4 },
      { name: 'Germán Cano', team: 'Fluminense', goals: 4, image: 'https://assets.goal.com/v3/assets/bltcc7a7ffd2fbf71f5/blt5e3e6e1c6b6c4f4a/60c5a5e6b7e7b5002c5e4f5a/german_cano.jpg', position: 5 },
      { name: 'Mohamed Salah', team: 'Liverpool', goals: 4, image: 'https://cdn.sofifa.net/players/209/331/25_120.png', position: 6 },
      { name: 'Endrick', team: 'Real Madrid', goals: 3, image: 'https://assets.goal.com/v3/assets/bltcc7a7ffd2fbf71f5/blt8b7e5c7a5f5c7a5f/64f5c7a5f5c7a5f5c7a5/endrick.jpg', position: 7 },
      { name: 'Lewandowski', team: 'Barcelona', goals: 3, image: 'https://cdn.sofifa.net/players/188/545/25_120.png', position: 8 },
      { name: 'Lautaro Martínez', team: 'Internazionale', goals: 2, image: 'https://cdn.sofifa.net/players/231/478/25_120.png', position: 9 },
      { name: 'Julián Álvarez', team: 'Atlético Madrid', goals: 2, image: 'https://cdn.sofifa.net/players/237/463/25_120.png', position: 10 }
    ];
  }

  /**
   * Atualiza a seção de artilheiros
   */
  updateTopScorers(scorers) {
    const container = document.getElementById('artilheiros-copa');
    if (!container) {
      console.warn('⚠️ Container #artilheiros-copa não encontrado');
      return;
    }

    // Valida e corrige os dados
    const validScorers = this.validateAndFixScorers(scorers);

    // Ordena artilheiros por gols (decrescente) e depois por posição
    const sortedScorers = [...validScorers].sort((a, b) => {
      // Primeiro critério: número de gols (decrescente)
      if (b.goals !== a.goals) {
        return b.goals - a.goals;
      }
      // Segundo critério: posição original (crescente)
      return (a.position || 0) - (b.position || 0);
    });

    // Atualiza dados globais dos artilheiros com ranking correto
    window.artilheiros = sortedScorers.map((scorer, index) => ({
      name: scorer.name,
      team: scorer.team,
      goals: scorer.goals,
      logo: scorer.image,
      position: index + 1 // Posição baseada na ordenação correta
    }));

    // Re-renderiza a seção
    container.innerHTML = '';
    window.artilheiros.forEach((artilheiro, idx) => {
      container.insertAdjacentHTML('beforeend', `
        <div class="flex-shrink-0 w-40 sm:w-48 md:w-56 rounded-lg bg-gradient-to-b from-neutral-900 to-neutral-800 shadow-lg overflow-hidden flex flex-col items-center justify-between border border-amber-500/20 top-scorer-card">
          <div class="flex flex-col items-center justify-center bg-gradient-to-r from-amber-600 to-amber-500 p-4 w-full">
            <div class="text-white text-2xl font-bold mb-2">#${artilheiro.position}</div>
            <img src="${artilheiro.logo}" alt="${artilheiro.name}" class="w-16 h-16 rounded-full border-2 border-white mb-2 object-cover shadow-md" 
                 onerror="this.src='https://via.placeholder.com/64x64/E9C043/000000?text=${artilheiro.name.charAt(0)}'">
            <span class="block text-sm font-semibold text-white/80">${artilheiro.team}</span>
            <span class="block text-2xl font-bold text-white">${artilheiro.goals} <span class='text-base font-normal'>gols</span></span>
          </div>
          <div class="p-3 w-full text-center">
            <h4 class="text-base font-semibold text-amber-300">${artilheiro.name}</h4>
          </div>
        </div>
      `);
    });

    console.log(`🥅 ${sortedScorers.length} artilheiros atualizados (ranking: ${sortedScorers.slice(0, 3).map(s => `${s.name}: ${s.goals}`).join(', ')}...)`);
  }

  /**
   * Atualiza a classificação dos grupos
   */
  updateStandings(standings) {
    if (!standings.length) return;

    // Converte formato do Globo para o formato da página
    const convertedStandings = standings.map(group => 
      group.teams.map(team => ({
        team: { 
          name: team.name, 
          logo: `https://via.placeholder.com/64x64/00a86b/ffffff?text=${team.name.substring(0, 2).toUpperCase()}`
        },
        points: team.points,
        rank: team.position,
        all: {
          played: team.played,
          win: team.wins,
          draw: team.draws,
          lose: team.losses,
          goals: {
            for: team.goalsFor,
            against: team.goalsAgainst
          }
        },
        goalsDiff: team.goalsFor - team.goalsAgainst,
        group: group.group
      }))
    );

    // Atualiza dados globais
    window.standingsGroupsData = convertedStandings;

    // Re-renderiza se as funções existem
    if (typeof renderGroupSelect === 'function') {
      renderGroupSelect(convertedStandings);
    }
    if (typeof renderStandingsGroup === 'function') {
      renderStandingsGroup(0);
    }

    console.log(`🏆 ${standings.length} grupos de classificação atualizados`);
  }

  /**
   * Atualiza os jogos/fixtures
   */
  updateFixtures(matches) {
    const container = document.getElementById('fixtures-copa');
    if (!container || !matches.length) return;

    container.innerHTML = '';
    matches.forEach(match => {
      const statusClass = match.status === 'Finalizado' ? 'bg-green-500' : 
                         match.status === 'Ao vivo' ? 'bg-red-500 animate-pulse' : 'bg-blue-500';
      
      container.insertAdjacentHTML('beforeend', `
        <div class="fixture-card flex justify-between items-center bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500 hover:border-blue-600 transition-all duration-300">
          <div class="flex flex-col">
            <span class="font-semibold text-gray-800">${match.date}</span>
            <span class="text-sm text-gray-500">${match.time}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="font-medium text-gray-700">${match.homeTeam}</span>
            <span class="font-bold text-blue-700 px-2">${match.score || 'VS'}</span>
            <span class="font-medium text-gray-700">${match.awayTeam}</span>
          </div>
          <div class="w-3 h-3 ${statusClass} rounded-full" title="${match.status}"></div>
        </div>
      `);
    });

    console.log(`⚽ ${matches.length} jogos atualizados`);
  }

  /**
   * Mostra indicador de atualização bem-sucedida
   */
  showUpdateIndicator(timestamp) {
    // Remove indicador existente
    const existing = document.getElementById('update-indicator');
    if (existing) existing.remove();

    // Cria novo indicador
    const indicator = document.createElement('div');
    indicator.id = 'update-indicator';
    indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 opacity-0 transition-opacity duration-300';
    indicator.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>Dados atualizados</span>
      </div>
      <div class="text-xs opacity-75">${new Date(timestamp).toLocaleTimeString('pt-BR')}</div>
    `;

    document.body.appendChild(indicator);

    // Anima entrada
    setTimeout(() => indicator.style.opacity = '1', 100);

    // Remove após 3 segundos
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => indicator.remove(), 300);
    }, 3000);
  }

  /**
   * Mostra indicador de erro
   */
  showErrorIndicator() {
    const existing = document.getElementById('error-indicator');
    if (existing) existing.remove();

    const indicator = document.createElement('div');
    indicator.id = 'error-indicator';
    indicator.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 opacity-0 transition-opacity duration-300';
    indicator.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>Erro ao atualizar</span>
      </div>
    `;

    document.body.appendChild(indicator);
    setTimeout(() => indicator.style.opacity = '1', 100);
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => indicator.remove(), 300);
    }, 5000);
  }

  /**
   * Configura atualização automática
   */
  startAutoUpdate() {
    this.intervalId = setInterval(() => {
      this.updatePageData();
    }, this.updateInterval);
    
    console.log(`⏰ Atualização automática configurada (${this.updateInterval / 1000}s)`);
  }

  /**
   * Para atualização automática
   */
  stopAutoUpdate() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('⏹️ Atualização automática parada');
    }
  }

  /**
   * Configura botão de atualização manual
   */
  setupManualUpdate() {
    // Cria botão de atualização manual se não existir
    if (!document.getElementById('manual-update-btn')) {
      const button = document.createElement('button');
      button.id = 'manual-update-btn';
      button.className = 'fixed bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg z-50 transition-colors duration-200';
      button.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
      `;
      button.title = 'Atualizar dados manualmente';
      
      button.addEventListener('click', () => {
        button.disabled = true;
        button.innerHTML = `
          <svg class="w-6 h-6 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        `;
        
        this.updatePageData().finally(() => {
          button.disabled = false;
          button.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          `;
        });
      });
      
      document.body.appendChild(button);
    }
  }
  /**
   * Limpa recursos
   */
  destroy() {
    this.stopAutoUpdate();
    
    const button = document.getElementById('manual-update-btn');
    if (button) button.remove();
    
    const indicator = document.getElementById('update-indicator');
    if (indicator) indicator.remove();
    
    const errorIndicator = document.getElementById('error-indicator');
    if (errorIndicator) errorIndicator.remove();
  }

  /**
   * Função de debug para testar o ranking manualmente
   */
  debugRanking() {
    console.group('🔍 Debug do Ranking de Artilheiros');
    
    const container = document.getElementById('artilheiros-copa');
    console.log('Container encontrado:', !!container);
    
    if (window.artilheiros) {
      console.log('Artilheiros atuais (window.artilheiros):');
      console.table(window.artilheiros.map(a => ({
        Posição: a.position,
        Nome: a.name,
        Time: a.team,
        Gols: a.goals
      })));
    }
    
    this.scraper.getData(false).then(data => {
      console.log('Dados do scraper:');
      console.table(data.artilheiros?.map(a => ({
        Posição: a.position,
        Nome: a.name,
        Time: a.team,
        Gols: a.goals
      })));
      
      console.log('Fonte dos dados:', data.source || 'API');
    });
    
    console.groupEnd();
  }
}

// Exporta para uso global
window.LandingPageIntegration = LandingPageIntegration;

// Auto-inicialização quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
  // Apenas inicializa se estivermos em uma página do Mundial de Clubes
  if (document.title.includes('Copa do Mundo de Clubes') || 
      document.body.innerHTML.includes('artilheiros-copa') ||
      document.body.innerHTML.includes('Mundial de Clubes')) {
    
    const integration = new LandingPageIntegration();
    integration.init();
    
    // Salva referência global para controle manual
    window.mundialIntegration = integration;
    
    // Função global para debug
    window.debugRanking = () => integration.debugRanking();
    
    console.log('🚀 Integração do Mundial de Clubes inicializada');
    console.log('💡 Use debugRanking() no console para verificar o ranking');
  }
});

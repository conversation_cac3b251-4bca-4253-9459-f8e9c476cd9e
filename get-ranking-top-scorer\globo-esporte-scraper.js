/**
 * Script para consumir dados do Mundial de Clubes via Globo Esporte
 * Extrai informações sobre artilheiros, classificações e jogos
 */

class GloboEsporteScraper {
  constructor() {
    this.baseUrl = 'https://ge.globo.com/futebol/mundial-de-clubes/';
    this.corsProxy = 'https://api.allorigins.win/get?url=';
    this.headers = {
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "accept-language": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
      "cache-control": "max-age=0",
      "priority": "u=0, i",
      "sec-ch-ua": "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "\"Windows\"",
      "sec-fetch-dest": "document",
      "sec-fetch-mode": "navigate",
      "sec-fetch-site": "same-origin",
      "sec-fetch-user": "?1",
      "upgrade-insecure-requests": "1"
    };
  }

  /**
   * Faz requisição para a página do Globo Esporte
   */
  async fetchGloboData() {
    try {
      console.log('🔄 Buscando dados do Globo Esporte...');
      
      // Usando CORS proxy para evitar problemas de CORS
      const proxyUrl = `${this.corsProxy}${encodeURIComponent(this.baseUrl)}`;
      
      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const htmlContent = data.contents;
      
      console.log('✅ Dados obtidos com sucesso');
      return this.parseHtmlContent(htmlContent);
      
    } catch (error) {
      console.error('❌ Erro ao buscar dados do Globo Esporte:', error);
      return this.getFallbackData();
    }
  }

  /**
   * Faz parsing do conteúdo HTML para extrair dados úteis
   */
  parseHtmlContent(html) {
    try {
      // Cria um parser DOM para processar o HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      const data = {
        artilheiros: this.extractTopScorers(doc),
        classificacao: this.extractStandings(doc),
        jogos: this.extractMatches(doc),
        noticias: this.extractNews(doc),
        timestamp: new Date().toISOString()
      };

      console.log('📊 Dados extraídos:', data);
      return data;
      
    } catch (error) {
      console.error('❌ Erro ao processar HTML:', error);
      return this.getFallbackData();
    }
  }

  /**
   * Extrai informações dos artilheiros
   */
  extractTopScorers(doc) {
    const scorers = [];
    
    try {
      // Procura por elementos que contenham informações de artilheiros
      const scorerElements = doc.querySelectorAll('.artilheiro, .goleador, .top-scorer, [data-cy="top-scorer"]');
      
      scorerElements.forEach((element, index) => {
        const name = this.extractText(element, '.nome, .name, .player-name, h3, h4');
        const team = this.extractText(element, '.equipe, .team, .clube');
        const goals = this.extractNumber(element, '.gols, .goals, .goal-count');
        const image = this.extractImage(element);
        
        if (name && goals !== null) {
          scorers.push({
            position: index + 1,
            name: name.trim(),
            team: team?.trim() || 'N/A',
            goals: goals,
            image: image || this.getDefaultPlayerImage()
          });
        }
      });

      // Se não encontrou pelo seletor específico, tenta buscar em tabelas
      if (scorers.length === 0) {
        const tables = doc.querySelectorAll('table');
        tables.forEach(table => {
          const rows = table.querySelectorAll('tr');
          rows.forEach((row, index) => {
            if (index === 0) return; // Skip header
            
            const cells = row.querySelectorAll('td, th');
            if (cells.length >= 3) {
              const name = cells[0]?.textContent?.trim();
              const team = cells[1]?.textContent?.trim();
              const goals = parseInt(cells[2]?.textContent?.trim());
              
              if (name && !isNaN(goals)) {
                scorers.push({
                  position: index,
                  name,
                  team: team || 'N/A',
                  goals,
                  image: this.getDefaultPlayerImage()
                });
              }
            }
          });
        });
      }
        } catch (error) {
      console.error('Erro ao extrair artilheiros:', error);
    }
    
    // Ordena os artilheiros por gols (decrescente) e depois por nome
    const sortedScorers = scorers.sort((a, b) => {
      if (b.goals !== a.goals) {
        return b.goals - a.goals; // Mais gols primeiro
      }
      return a.name.localeCompare(b.name); // Alfabético se mesma quantidade de gols
    });

    // Atualiza as posições após ordenação
    const finalScorers = sortedScorers.map((scorer, index) => ({
      ...scorer,
      position: index + 1
    }));
    
    return finalScorers.length > 0 ? finalScorers : this.getDefaultTopScorers();
  }

  /**
   * Extrai classificação dos grupos
   */
  extractStandings(doc) {
    const standings = [];
    
    try {
      const groupElements = doc.querySelectorAll('.grupo, .group, .classificacao');
      
      groupElements.forEach((groupEl, groupIndex) => {
        const groupName = this.extractText(groupEl, '.nome-grupo, .group-name, h2, h3') || `Grupo ${String.fromCharCode(65 + groupIndex)}`;
        const teams = [];
        
        const teamRows = groupEl.querySelectorAll('tr, .time, .team');
        teamRows.forEach((row, index) => {
          const teamName = this.extractText(row, '.nome-time, .team-name, .nome');
          const points = this.extractNumber(row, '.pontos, .points, .pts');
          
          if (teamName) {
            teams.push({
              position: index + 1,
              name: teamName.trim(),
              points: points || 0,
              played: this.extractNumber(row, '.jogos, .played, .j') || 0,
              wins: this.extractNumber(row, '.vitorias, .wins, .v') || 0,
              draws: this.extractNumber(row, '.empates, .draws, .e') || 0,
              losses: this.extractNumber(row, '.derrotas, .losses, .d') || 0,
              goalsFor: this.extractNumber(row, '.gols-pro, .goals-for, .gf') || 0,
              goalsAgainst: this.extractNumber(row, '.gols-contra, .goals-against, .ga') || 0
            });
          }
        });
        
        if (teams.length > 0) {
          standings.push({
            group: groupName,
            teams: teams
          });
        }
      });
      
    } catch (error) {
      console.error('Erro ao extrair classificação:', error);
    }
    
    return standings.length > 0 ? standings : this.getDefaultStandings();
  }

  /**
   * Extrai informações dos jogos
   */
  extractMatches(doc) {
    const matches = [];
    
    try {
      const matchElements = doc.querySelectorAll('.jogo, .partida, .match, .fixture');
      
      matchElements.forEach(element => {
        const homeTeam = this.extractText(element, '.time-casa, .home-team, .home');
        const awayTeam = this.extractText(element, '.time-visitante, .away-team, .away');
        const date = this.extractText(element, '.data, .date, .when');
        const time = this.extractText(element, '.hora, .time, .horario');
        const score = this.extractText(element, '.placar, .score, .resultado');
        
        if (homeTeam && awayTeam) {
          matches.push({
            homeTeam: homeTeam.trim(),
            awayTeam: awayTeam.trim(),
            date: date?.trim(),
            time: time?.trim(),
            score: score?.trim(),
            status: this.extractText(element, '.status, .situacao')?.trim()
          });
        }
      });
      
    } catch (error) {
      console.error('Erro ao extrair jogos:', error);
    }
    
    return matches.length > 0 ? matches : this.getDefaultMatches();
  }

  /**
   * Extrai notícias relacionadas
   */
  extractNews(doc) {
    const news = [];
    
    try {
      const newsElements = doc.querySelectorAll('.noticia, .news, article, .post');
      
      newsElements.forEach(element => {
        const title = this.extractText(element, 'h1, h2, h3, .titulo, .title');
        const summary = this.extractText(element, '.resumo, .summary, .excerpt, p');
        const link = element.querySelector('a')?.href;
        const image = this.extractImage(element);
        
        if (title) {
          news.push({
            title: title.trim(),
            summary: summary?.trim().substring(0, 200),
            link: link,
            image: image,
            timestamp: new Date().toISOString()
          });
        }
      });
      
    } catch (error) {
      console.error('Erro ao extrair notícias:', error);
    }
    
    return news.slice(0, 10); // Limit to 10 news items
  }

  /**
   * Métodos auxiliares para extração de dados
   */
  extractText(element, selector) {
    try {
      const found = element.querySelector(selector);
      return found ? found.textContent : element.textContent;
    } catch {
      return null;
    }
  }

  extractNumber(element, selector) {
    const text = this.extractText(element, selector);
    const number = parseInt(text?.replace(/\D/g, ''));
    return isNaN(number) ? null : number;
  }

  extractImage(element) {
    try {
      const img = element.querySelector('img');
      return img ? img.src : null;
    } catch {
      return null;
    }
  }

  /**
   * Dados de fallback caso a requisição falhe
   */
  getFallbackData() {
    return {
      artilheiros: this.getDefaultTopScorers(),
      classificacao: this.getDefaultStandings(),
      jogos: this.getDefaultMatches(),
      noticias: [],
      timestamp: new Date().toISOString(),
      source: 'fallback'
    };
  }

  getDefaultTopScorers() {
    return [
      { position: 1, name: 'Erling Haaland', team: 'Manchester City', goals: 8, image: 'https://cdn.sofifa.net/players/239/085/25_120.png' },
      { position: 2, name: 'Kylian Mbappé', team: 'Real Madrid', goals: 7, image: 'https://cdn.sofifa.net/players/231/747/25_120.png' },
      { position: 3, name: 'Vinicius Jr', team: 'Real Madrid', goals: 6, image: 'https://cdn.sofifa.net/players/238/794/25_120.png' },
      { position: 4, name: 'Lionel Messi', team: 'Inter Miami', goals: 5, image: 'https://cdn.sofifa.net/players/158/023/25_120.png' },
      { position: 5, name: 'Germán Cano', team: 'Fluminense', goals: 4, image: 'https://assets.goal.com/v3/assets/bltcc7a7ffd2fbf71f5/blt5e3e6e1c6b6c4f4a/60c5a5e6b7e7b5002c5e4f5a/german_cano.jpg' }
    ];
  }

  getDefaultStandings() {
    return [
      {
        group: 'Grupo A',
        teams: [
          { position: 1, name: 'Manchester City', points: 9, played: 3, wins: 3, draws: 0, losses: 0, goalsFor: 8, goalsAgainst: 2 },
          { position: 2, name: 'Juventus', points: 6, played: 3, wins: 2, draws: 0, losses: 1, goalsFor: 5, goalsAgainst: 3 },
          { position: 3, name: 'Wydad AC', points: 3, played: 3, wins: 1, draws: 0, losses: 2, goalsFor: 3, goalsAgainst: 5 },
          { position: 4, name: 'Al Ain FC', points: 0, played: 3, wins: 0, draws: 0, losses: 3, goalsFor: 1, goalsAgainst: 7 }
        ]
      }
    ];
  }

  getDefaultMatches() {
    return [
      { homeTeam: 'Manchester City', awayTeam: 'Al Ain FC', date: '2025-06-15', time: '16:00', score: '3-0', status: 'Finalizado' },
      { homeTeam: 'Real Madrid', awayTeam: 'Pachuca', date: '2025-06-16', time: '19:00', score: '2-1', status: 'Finalizado' },
      { homeTeam: 'Chelsea', awayTeam: 'León', date: '2025-06-17', time: '16:00', score: '', status: 'Agendado' }
    ];
  }

  getDefaultPlayerImage() {
    return 'https://via.placeholder.com/120x120/E9C043/000000?text=Player';
  }

  /**
   * Salva os dados em localStorage para cache
   */
  saveToCache(data) {
    try {
      localStorage.setItem('globo_mundial_data', JSON.stringify(data));
      localStorage.setItem('globo_mundial_timestamp', Date.now().toString());
      console.log('💾 Dados salvos no cache');
    } catch (error) {
      console.error('Erro ao salvar cache:', error);
    }
  }

  /**
   * Carrega dados do cache se disponível
   */
  loadFromCache() {
    try {
      const timestamp = localStorage.getItem('globo_mundial_timestamp');
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;
      
      if (timestamp && (now - parseInt(timestamp)) < fiveMinutes) {
        const cachedData = localStorage.getItem('globo_mundial_data');
        if (cachedData) {
          console.log('📦 Dados carregados do cache');
          return JSON.parse(cachedData);
        }
      }
    } catch (error) {
      console.error('Erro ao carregar cache:', error);
    }
    return null;
  }

  /**
   * Método principal para obter dados
   */
  async getData(useCache = true) {
    // Tenta carregar do cache primeiro
    if (useCache) {
      const cachedData = this.loadFromCache();
      if (cachedData) {
        return cachedData;
      }
    }

    // Busca dados frescos
    const data = await this.fetchGloboData();
    
    // Salva no cache
    this.saveToCache(data);
    
    return data;
  }
}

// Exporta para uso global
window.GloboEsporteScraper = GloboEsporteScraper;

// Exemplo de uso
async function exemploUso() {
  const scraper = new GloboEsporteScraper();
  
  try {
    console.log('🚀 Iniciando scraping do Globo Esporte...');
    const dados = await scraper.getData();
    
    console.log('📈 Artilheiros:', dados.artilheiros);
    console.log('🏆 Classificação:', dados.classificacao);
    console.log('⚽ Jogos:', dados.jogos);
    console.log('📰 Notícias:', dados.noticias);
    
    return dados;
  } catch (error) {
    console.error('❌ Erro no exemplo:', error);
  }
}

// Auto-execução para teste (comentar em produção)
// exemploUso();

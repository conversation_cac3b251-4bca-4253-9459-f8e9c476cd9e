<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer>
  // Funções compartilhadas
  function waitForElement(selector, timeout = 10000) {
      return new Promise((resolve, reject) => {
          const observer = new MutationObserver(() => {
              const element = document.querySelector(selector);
              if (element) {
                  resolve(element);
                  observer.disconnect();
              }
          });

          observer.observe(document.body, { childList: true, subtree: true });
      });
  }

  function formatDate(date) {
      const options = {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          timeZone: 'America/Sao_Paulo'
      };
      return new Intl.DateTimeFormat('pt-BR', options).format(date);
  }

  // Countdown
  function startCountdown(startDate, endDate) {
      const intervalId = setInterval(() => {
          const initElements = document.querySelectorAll('[id="init-day"]');
          const endElements = document.querySelectorAll('[id="end-day"]');

          if (initElements.length > 0 && endElements.length > 0) {
              clearInterval(intervalId);
              endElements.forEach(element => element.innerHTML = formatDate(endDate));
              initElements.forEach(element => element.innerHTML = formatDate(startDate));
          }
      }, 100);
  }

// Slider Motion

  function initSliderMotion(father_id) {
  waitForElement(father_id).then((slider) => {
      const config = {
          content: `
              <div class="flex items-center justify-start text-blue-950 text-base font-bold font-['Inter'] uppercase leading-relaxed whitespace-nowrap">
                  <span style="color: #172554;">ESCOLHA SEU FAVORITO</span>
                  <div class="w-2 h-2x mx-4"></div>
                  <span style="color: #172554;">*</span>
                  <div class="w-2 h-2 mx-4"></div>
                  <span style="color: #172554;">APOSTE A PARTIR DE R$10</span>
                  <div class="w-2 h-2 mx-4"></div>
                  <span style="color: #172554;">*</span>
                  <div class="w-2 h-2 mx-4"></div>
                  <span style="color: #172554;">GANHE A CADA CHUTE NO GOL</span>
                  <div class="w-2 h-2 mx-4"></div>
                  <span style="color: #172554;">*</span>
                  <div class="w-2 h-2 mx-4"></div>
              </div>
          `,
          repeats: 16,
          speed: 1,
          mobileSpeed: 0.5,
          useRAF: true
      };

      slider.innerHTML = config.content.repeat(config.repeats);
      slider.style.display = 'flex';
      slider.style.willChange = 'transform';

      let position = 0;
      let animationId;
      let lastTimestamp = 0;
      const isMobile = window.innerWidth <= 768;
      const currentSpeed = isMobile ? config.mobileSpeed : config.speed;

      const animate = (timestamp) => {
          if (!lastTimestamp) lastTimestamp = timestamp;
          const deltaTime = timestamp - lastTimestamp;
          lastTimestamp = timestamp;

          position -= currentSpeed * (deltaTime / 16);

          if (position <= -slider.firstElementChild.offsetWidth) {
              slider.firstElementChild.remove();
              slider.appendChild(createContentElement(config.content));
              position += slider.firstElementChild.offsetWidth;
          }

          slider.style.transform = `translateX(${position}px)`;
          animationId = requestAnimationFrame(animate);
      };

      const createContentElement = (html) => {
          const div = document.createElement('div');
          div.innerHTML = html;
          return div.firstElementChild;
      };

      if (config.useRAF) {
          animationId = requestAnimationFrame(animate);
      } else {
          setInterval(() => {
              position -= currentSpeed;
              
              if (position <= -slider.firstElementChild.offsetWidth) {
                  slider.firstElementChild.remove();
                  slider.appendChild(createContentElement(config.content));
                  position = 0;
              }
              
              slider.style.transform = `translateX(${position}px)`;
          }, 16);
      }

      new MutationObserver((mutations, obs) => {
          if (!document.contains(slider)) {
              if (config.useRAF) {
                  cancelAnimationFrame(animationId);
              }
              obs.disconnect();
          }
      }).observe(document.body, { childList: true, subtree: true });

      let resizeTimeout;
      window.addEventListener('resize', () => {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(() => {
              position = position % slider.firstElementChild.offsetWidth;
          }, 100);
      });
  });
}

function setupSliderDrag() {
  return new Promise((resolve) => {
      const checkSlider = () => {
          const sliders = [
              document.querySelector('#slider-artilheiros'),
              document.querySelector('#slider-results-artilheiros')
          ];

          // Verifica se ambos os sliders existem e têm filhos
          const slidersReady = sliders.every(slider => {
              return slider && slider.children.length > 0;
          });

          if (!slidersReady) {
              setTimeout(checkSlider, 100);
              return;
          }

          console.log('Configurando drag para os sliders');
          
          let isDown = false;
          let startX;
          let scrollLeft;

          const handleMouseDown = (e, slider) => {
              isDown = true;
              slider.style.cursor = 'grabbing';
              startX = e.pageX - slider.getBoundingClientRect().left;
              scrollLeft = slider.scrollLeft;
              e.preventDefault(); // Prevenir seleção de texto durante o drag
          };

          const handleMouseLeave = (slider) => {
              isDown = false;
              slider.style.cursor = 'grab';
          };

          const handleMouseUp = (slider) => {
              isDown = false;
              slider.style.cursor = 'grab';
          };

          const handleMouseMove = (e, slider) => {
              if (!isDown) return;
              e.preventDefault();
              const x = e.pageX - slider.getBoundingClientRect().left;
              const walk = (x - startX) * 2;
              slider.scrollLeft = scrollLeft - walk;
          };

          const setupSlider = (slider) => {
              // Remove event listeners existentes para evitar duplicação
              const newSlider = slider.cloneNode(true);
              slider.parentNode.replaceChild(newSlider, slider);
              
              // Configura os novos listeners
              newSlider.addEventListener('mousedown', (e) => handleMouseDown(e, newSlider));
              newSlider.addEventListener('mouseleave', () => handleMouseLeave(newSlider));
              newSlider.addEventListener('mouseup', () => handleMouseUp(newSlider));
              newSlider.addEventListener('mousemove', (e) => handleMouseMove(e, newSlider));

              // Configura estilos
              newSlider.style.cursor = 'grab';
              newSlider.style.scrollBehavior = 'smooth';
              newSlider.style.overflowX = 'auto';
              newSlider.style.scrollSnapType = 'x mandatory';
              
              // Configura os filhos para snap
              Array.from(newSlider.children).forEach(child => {
                  child.style.scrollSnapAlign = 'start';
              });
          };

          sliders.forEach(slider => {
              if (slider) {
                  setupSlider(slider);
              }
          });

          resolve();
      };

      checkSlider();
  });
}

  // Insert Options com Promise
  function insertOptions() {
      return new Promise((resolve) => {
          const now = new Date();
          const expireTime = new Date(2025, 4, 11, 16, 0)
          const proxJogos = [
              {
                  jogador: 'KAIO JORGE',
                  confronto: `Sport x Cruzeiro (Série A)`,
                  horaConfronto: new Date(2025, 4, 11, 16, 0),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/uZVgO8SX6tLR15PLXT9KYw.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '4.08',
                  newOdd: '4.40',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=5e3ebb5e-214e-4a5a-9358-217836415357"
              },
              {
                  jogador: 'VITOR ROQUE',
                  confronto: `Palmeiras x São Paulo (Série A)`,
                  horaConfronto: new Date(2025, 4, 11, 17, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/UUGj2rNLoxQM2DElHTXiyQ.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '3.10',
                  newOdd: '3.50',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=1dc43c4c-f2f3-4b8f-9df1-206035624f76"
              },
              {
                  jogador: 'LUCIANO',
                  confronto: `Palmeiras x São Paulo (Série A)`,
                  horaConfronto: new Date(2025, 4, 11, 17, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/MEWMwvasZ6SQUdJdx-7hNA.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '6.95',
                  newOdd: '7.20',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=b94161a7-1e8d-43f2-a41d-e9cc4fa7ba35"
              },
              {
                  jogador: 'HULK',
                  confronto: `Atlético MG x Fluminense (Série A)`,
                  horaConfronto: new Date(2025, 4, 11, 17, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/E-tszk0rEEW7C03mz7uIBg.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '2.44',
                  newOdd: '2.80',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=2dc4125c-3652-4f00-a3a7-36d2799785a1"
              },
                {
                  jogador: 'ARIAS',
                  confronto: `Atlético MG x Fluminense (Série A)`,
                  horaConfronto: new Date(2025, 4, 11, 17, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/yi2DQUOsYPgcELK9AeqYWg.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '6.00',
                  newOdd: '6.30',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=724cbf8b-1008-4fab-97fb-7838e22834fa"
              }/*, 
              {
                  jogador: 'DUDU',
                  confronto: `SÃO PAULO X CRUZEIRO (Série A)`,
                  horaConfronto: new Date(2025, 3, 13, 17, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/dePCFnAAxXL1xbB6CvTu_A.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '6.20',
                  newOdd: '6.45',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=ae8b022d-3198-49f1-ad3c-a5b633fe6fe8"
              },
              {
                  jogador: 'JOHN ARIAS',
                  confronto: `Fluminense x Santos (Série A)`,
                  horaConfronto: new Date(2025, 3, 13, 19, 30),
                  image: 'https://next-minio-b2b.devcore.at/next-cms/yi2DQUOsYPgcELK9AeqYWg.png',
                  mercado: 'Marcador a qualquer momento',
                  oldOdd: '4.18',
                  newOdd: '4.45',
                  oddLink: "https://casadeapostas.bet.br/sports?referenceId=36180d61-9724-440d-b793-dac317a8f624"
              }    */
          ];

          const rodadasFinalizadas = [
              {
                  rodada: "RODADA 1 | 29/03",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$10 em aposta grátis a cada gol do seu artilheiro",
                  resultados: [
                      { jogador: "VITOR ROQUE", premio: "R$0" },
                      { jogador: "TIQUINHO SOARES", premio: "R$0" },
                      { jogador: "VEGETTI", premio: "R$10" },
                      { jogador: "ERICK PULGA", premio: "R$0" },
                      { jogador: "YURI ALBERTO", premio: "R$0" }
                  ]
              },{
                  rodada: "RODADA 2 | 06/04",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "BRUNO HENRIQUE", premio: "R$10" },
                      { jogador: "HULK", premio: "R$10" },
                      { jogador: "TIQUINHO SOARES", premio: "R$5" },
                      { jogador: "GABIGOL", premio: "R$0" },
                      { jogador: "CANO", premio: "R$0" }
                  ]
              },{
                  rodada: "RODADA 3 | 13/04",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "LUCIANO", premio: "R$0" },
                      { jogador: "ARRASCAETA", premio: "R$10" },
                      { jogador: "RONY", premio: "R$15" },
                      { jogador: "NEYMAR", premio: "R$5" },
                      { jogador: "ENNER VALENCIA", premio: "R$0" }
                  ]
              },{
                  rodada: "RODADA 4 | 16/04",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "PEDRO", premio: "R$15" },
                      { jogador: "VITOR ROQUE", premio: "R$5" },
                      { jogador: "YURI ALBERTO", premio: "R$0" },
                      { jogador: "HULK", premio: "R$5" },
                      { jogador: "NEYMAR", premio: "R$0" }
                  ]
              },{
                  rodada: "RODADA 5 | 20/04",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "CANO", premio: "R$10" },
                      { jogador: "MATHEUSINHO", premio: "R$5" },
                      { jogador: "EDUARDO SASHA", premio: "R$0" },
                      { jogador: "PAULINHO", premio: "R$0" },
                      { jogador: "MARINHO", premio: "R$0" }
                  ]
              },{
                  rodada: "RODADA 6 | 27/04",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "PEDRO", premio: "R$15" },
                      { jogador: "MATHEUS PEREIRA", premio: "R$10" },
                      { jogador: "YURI ALBERTO", premio: "R$0" },
                      { jogador: "TIQUINHO", premio: "R$0" },
                      { jogador: "VEGETTI", premio: "R$0" }
                  ]
              },,{
                  rodada: "RODADA 7 | 04/05",
                  campeonato: "BRASILEIRÃO 2025",
                  premio: "R$5 em aposta grátis a cada chute no gol do seu artilheiro",
                  resultados: [
                      { jogador: "KAIO JORGE", premio: "R$15" },
                      { jogador: "PEDRO", premio: "R$5" },
                      { jogador: "BRAITHWAITE", premio: "R$5" },
                      { jogador: "ESTEVÃO", premio: "R$0" },
                      { jogador: "VEGETTI", premio: "R$0" }
                  ]
              },
          ]

          const sliderSup = document.getElementById('slider-artilheiros');
          const sliderResults = document.getElementById('slider-results-artilheiros');
          if (!sliderSup || !sliderResults) {
              setTimeout(() => insertOptions().then(resolve), 100);
              return;
          }

          function formatTime(date) {
              const day = String(date.getDate()).padStart(2, '0');
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const hours = String(date.getHours()).padStart(2, '0');
              const minutes = String(date.getMinutes()).padStart(2, '0');
              const year = String(date.getFullYear()).slice(-2);

              const today = new Date(now);
              today.setHours(0, 0, 0, 0);

              const tomorrow = new Date(today);
              tomorrow.setDate(today.getDate() + 1);

              if (date >= today && date < tomorrow) {
                  return `Hoje às ${hours}h${minutes}`;
              }

              if (date >= tomorrow && date < new Date(tomorrow).setDate(tomorrow.getDate() + 1)) {
                  return `Amanhã às ${hours}h${minutes}`;
              }

              return `${day}/${month}/${year} às ${hours}h${minutes}`;
          }

          const isValidOption = (item) => {
              const twoMinutesBefore = new Date(item.horaConfronto.getTime() - 2 * 60000);
              return now <= twoMinutesBefore;
          };

          function generateOddsMarkup(oldOdd, newOdd, oddLink) {
              return `
                      <div class="flex items-center w-full justify-center font-bold mt-auto relative mt-2" style="background: #A8F539;border-radius: 2rem;">
                          <a class="w-full rounded-md text-center px-2 py-2" style="color: #101014; href="${oddLink}">
                              <span class="strikethrough text-xs opacity-80 w-full">${oldOdd}</span> -> ${newOdd}
                          </a>
                      </div>
              `;
          }
          sliderSup.innerHTML = '';
          const jogosValidos = proxJogos.filter(isValidOption);
          if (jogosValidos.length === 0 || new Date() > expireTime) {
              sliderSup.insertAdjacentHTML('beforeend', `
                  <div class="w-[1280px] h-full max-h-fit inline-flex justify-start items-center gap-8 text-white">
                      Promoção encerrada!<br><br>
                      Aguarde o final da rodada para ver os resultados!
                  </div>
              `);
          } else {
              jogosValidos.forEach((item, i) => {
                  sliderSup.insertAdjacentHTML('beforeend', `
                      <div class="w-[1280px] h-full max-h-fit inline-flex justify-start p-4 items-center gap-8" style="background: linear-gradient(180deg, #24242D 0%, #101014 100%); border: 2px solid #2465FF; border-radius: 2rem;">
                          <div class="relative justify-between flex flex-col overflow-hidden" style="width: 275px; height: 300px;">
                              <img src="${item.image}" alt="${item.jogador}" class="absolute inset-0 w-full h-full z-0">
                              <div class="flex justify-center font-bold text-white rounded-full z-10 mt-1" style="max-width: 160px; background: #47485D;">
                                  <span class="text-sm py-1">${item.jogador}</span>
                              </div>
                              <div class="flex flex-col gap-3 mt-1 relative">
                                  <div class="flex flex-row rounded-full text-xs bg-primaryContrast items-center gap-1 relative z-10 py-1" style="width: fit-content;">
                                      <svg width="18" height="18" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M9.26872 9.27301L8.83539 8.53634L9.62622 6.16926L10.39 5.91467L10.9316 6.32092V6.39676C10.9316 6.43467 10.9479 6.46717 10.9479 6.49967C10.9479 7.56676 10.5904 8.50926 9.88081 9.32176L9.26872 9.27301ZM5.37956 8.12467L4.63747 5.94176L6.59831 4.56592L8.55914 5.94176L7.81706 8.12467H5.37956ZM6.59831 10.8493C6.12164 10.8493 5.67206 10.7734 5.24414 10.6218L4.87039 9.80384L5.22789 9.20801H7.99581L8.32622 9.80384L7.95247 10.6218C7.52456 10.7734 7.07497 10.8493 6.59831 10.8493ZM3.31581 9.32176C3.02872 8.98592 2.77956 8.53634 2.56831 7.98926C2.35706 7.43676 2.24872 6.93842 2.24872 6.49967C2.24872 6.46717 2.26497 6.43467 2.26497 6.39676V6.32092L2.80664 5.91467L3.57039 6.16926L4.36122 8.53634L3.92789 9.27301L3.31581 9.32176ZM6.05664 2.86509V3.62342L3.88997 5.12384L3.16414 4.89634L2.93664 4.15967C3.17497 3.79134 3.52706 3.42301 3.99289 3.06551C4.45872 2.70801 4.90289 2.47509 5.32539 2.35592L6.05664 2.86509ZM7.87122 2.35592C8.29372 2.47509 8.73789 2.70801 9.20372 3.06551C9.66956 3.42301 10.0216 3.79134 10.26 4.15967L10.0325 4.89634L9.30664 5.12926L7.13997 3.62884V2.86509L7.87122 2.35592ZM2.76872 2.67009C1.72331 3.73176 1.18164 5.01009 1.18164 6.49967C1.18164 7.98926 1.72331 9.26759 2.76872 10.3293C3.81414 11.3909 5.10872 11.9163 6.59831 11.9163C8.08789 11.9163 9.36622 11.3747 10.4279 10.3293C11.4896 9.28384 12.015 7.98926 12.015 6.49967C12.015 5.01009 11.4733 3.73176 10.4279 2.67009C9.38247 1.60842 8.08789 1.08301 6.59831 1.08301C5.10872 1.08301 3.83039 1.62467 2.76872 2.67009Z" fill="#101014"/>
                                      </svg>
                                      <span class="font-bold" style="color: #101014;>R$5</span>
                                      <span class=" text-xxs" style="color: #101014;>A CADA CHUTE NO GOL</span>
                                  </div>
                                  <div class="flex justify-center text-white relative z-10 rounded-full py-1 px-2" style="max-width: 200px; background: #47485D;">
                                      <span class="text-xs py-1">${item.mercado}</span>
                                  </div>
                              </div>
                          </div>
                          <div class="flex flex-col p-2 justify-start text-primaryContrast">
                              <span class="text-xs py-2">${item.confronto}<br>${formatTime(item.horaConfronto)}</span>
                              ${generateOddsMarkup(item.oldOdd, item.newOdd, item.oddLink)}
                          </div>
                      </div>
                  `);
              });
          }

          sliderResults.innerHTML = '';
          rodadasFinalizadas.forEach((item, i) => {
              sliderResults.insertAdjacentHTML('beforeend', `
                      <div class="inline-flex flex-col justify-start items-start" style="max-width: 300px; min-width: 300px;">
                          <div class="self-stretch min-h-20 px-8 py-4 rounded-tl-2xl rounded-tr-2xl flex flex-col justify-start items-start gap-2" style="border-radius: 1rem 1rem 0rem 0rem; background: linear-gradient(90deg, #2465FF 0%, #7EBEFF 32.94%, #4C92FF 71.12%, #2465FF 95.47%), #300D68;">
                              <div class="self-stretch flex flex-col justify-start items-start gap-2">
                                  <div class="flex flex-row justify-between items-center">
                                      <div class="text-xs font-bold"><span>${item.rodada}</span> <span>${item.campeonato}</span></div>
                                  </div>
                                  <div class="self-stretch justify-start text-neutral-900 text-xs font-normal font-['Inter'] leading-none tracking-tight">${item.premio}</div>
                              </div>
                          </div>
                          <div id="results_card" class="self-stretch rounded-bl-2xl rounded-br-2xl flex flex-col justify-start items-center gap-2 pb-1" style="border-radius: 0rem 0rem 1rem 1rem; background: #171717;">
                              ${item.resultados.length > 0
                                          ? item.resultados.map((jogador, i) => `
                                          <div class="flex flex-col justify-start items-center gap-3 ${i < (item.resultados.length - 1) ? 'border-b-[1px] border-neutral': 'border-none'} w-full" style="border-color: #E8E9EB;">
                                              <div class="self-stretch w-full inline-flex justify-center items-center gap-2">
                                                  <div class="w-full px-6 py-2 bg-lime-200 rounded-[64px] flex justify-center items-center gap-2 px-4">
                                                      <div class="w-full rounded-full text-center px-2 py-1" style="background: #d9f99d;">${jogador.jogador}</div>
                                                      <div class="w-full rounded-full text-center px-2 py-1" style="background: #a3e635;">${jogador.premio}</div>
                                                  </div>
                                              </div>
                                          </div>
                                          `).join('')
                                          : `<span class="text-xl py-2">Sem resultados</span>`
                                      }
                          </div>
                      </div>
                  `);
              });
          // Aguardar que o slider tenha filhos antes de chamar o resolve
          if (sliderSup.children.length === 0) {
              setTimeout(() => insertOptions().then(resolve), 100);
          } else {
              resolve();
          }
      });
  }

  artilheirosInterval = setInterval(() => {
      try {
          const startDate = new Date(2025, 4, 9);
          const endDate = new Date(2025, 4, 11);
          startCountdown(startDate, endDate);
          
          // Sliders Motion
          initSliderMotion('#slider-motion_A');
          initSliderMotion('#slider-motion_B');
          initSliderMotion('#slider-motion_C');
          insertOptions().then(() => {
                  setupSliderDrag();
              }).catch(error => console.error('Erro na inicialização:', error));
          const eventContainer = document.getElementById("slider-artilheiros");
          if (eventContainer.childElementCount !== 0) {
              clearInterval(artilheirosInterval);
          }
      } catch (err) {
          console.error(err)
      }
  }, 100);
</script>
    <title>Document</title>
</head>
<body>
    <style>
    #view-content>div {
        max-width: 100%;
    }

    #promo-artilheiros-page {
        padding: 0;

    }

    .light-green-bg {
        background-color: #E8E9EB;
    }

    .font-medium {
        white-space: initial;
    }

    .account-section-items {
        gap: 20px;
    }

    #page-container {
        max-width: 1520px;
    }

    #countdown {
        color: #75787B;
    }

    #premios {
        gap: 6px;
    }
    .slider-container {
        overflow: hidden;
        position: relative;
    }

    .slider {
        display: flex;
        backface-visibility: hidden;
        transform: translate3d(0, 0, 0);
    }

    #promo-details {
        display: grid;
    }

    details[open]>summary #plus-icon {
        display: none;
    }

    details[open]>summary #minus-icon {
        display: block;
    }

    details[open]>summary #info-icon {
        transform: rotate(180deg);
    }

    #firsts_places {
        width: 80%;
    }

    #botao-disputa {
        min-width: max-content;
        max-height: fit-content;
    }

    #slider-artilheiros {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
    }

    #slider-artilheiros::-webkit-scrollbar {
        display: none;
    }

    #slider-artilheiros>div {
        scroll-snap-align: start;
        flex-shrink: 0;
    }

    .responsive-image {
        display: block;
        width: 100%;
        height: auto;
    }

    .strikethrough {
        position: relative;
        font-weight: bold;
    }
    .strikethrough:before {
        position: absolute;
        content: "";
        left: 0;
        top: 50%;
        right: 0;
        border-top: 1.5px solid;
        border-color: #000;
        transform: rotate(-25deg);
        color: #000;
    }

    @media (max-width: 450px) {
        .account-section-items {
            grid-template-columns: 1fr;
        }

        #promo-artilheiros-page {
            border-collapse: collapse;
        }

        #promo-details {
            padding: 1rem 0;

            .account-section-items {
                gap: 0;
            }
        }

        #slider-artilheiros {
            gap: 12px;
        }

        #jogo-responsavel {
            gap: 10px;
        }

        #premios {
            flex-direction: column;
            font-size: 12px;
            font-weight: 400;
            line-height: 19.36px;
            margin: 0;
            gap: 2px;
        }

        #points {
            font-size: 1rem;
            line-height: 1.5rem;
        }

        #name {
            font-size: 1rem;
            line-height: 1.5rem;
        }

        .font-medium {
            font-size: 14px;
        }

        #botao-disputa {
            display: flex;
            justify-content: center;
            width: -webkit-fill-available;
            place-items: center;
            padding: 20.4px 30.6px 20.4px 30.6px;
        }

        #jogo-responsavel>span {
            font-size: 1.5rem;
            line-height: 2rem;
        }

        #container-countdown {
            flex-direction: row;
        }

        #countdown {
            flex-direction: row;
            font-weight: 500;
            font-size: 14px;
        }

        #fase_atual {
            display: flex;
            flex-direction: column-reverse;
            gap: 0;
        }

        #init-day,
        #end-day {
            font-size: 14px;
        }
    }
</style>
<img src="https://next-minio-b2b.devcore.at/next-cms/77nYbJuqtyVND_UxHZJ0eQ.webp" class="responsive-image"
    alt="Promo Page Image">
<div id="hidden-promo"
    class="flex flex-col justify-center items-center w-full flex-grow overflow-x-clip bg-contentMain p-4">
    <div id="page-container"
        class="flex flex-col flex-grow shrink-0 w-full relative min-h-[calc(var(--doc-height)-152px)]">
        <div class="flex flex-col justify-between mb-4 mt-10">
            <div class="space-x-2 flex-start items-center mt-8">
                <h1 class="flex-start text-4xl font-bold py-1">Artilheiro da Casa</h1>
            </div>
            <p>Aposte a partir de R$10 em uma das opções abaixo e ganhe R$5,00 em aposta grátis a cada chute do seu artilheiro.</p>
        </div>
    </div>
</div>
<div class="flex flex-row gap-4" style="background: #A8F539;">
    <div id="slider-motion_A" class="flex flex-row px-6 py-3">
        <div class="w-2 h-2x mx-4"></div>
        <div class="w-2 h-2 bg-blue-950">*</div>
        <div class="w-2 h-2x mx-4"></div>
        <div
            class="justify-start text-blue-950 text-base font-bold font-['Inter'] uppercase leading-relaxed whitespace-nowrap">
            ESCOLHA SEU FAVORITO</div>
    </div>
    <div id="slider-motion_B" class="flex flex-row px-6 py-3">
        <div class="w-2 h-2x mx-4"></div>
        <div class="w-2 h-2 bg-blue-950">*</div>
        <div class="w-2 h-2x mx-4"></div>
        <div
            class="justify-start text-blue-950 text-base font-bold font-['Inter'] uppercase leading-relaxed whitespace-nowrap">
            APOSTE A PARTIR DE R$10</div>
    </div>
    <div id="slider-motion_C" class="flex flex-row px-6 py-3">
        <div class="w-2 h-2x mx-4"></div>
        <div class="w-2 h-2 bg-blue-950">*</div>
        <div class="w-2 h-2x mx-4"></div>
        <div
            class="justify-start text-blue-950 text-base font-bold font-['Inter'] uppercase leading-relaxed whitespace-nowrap">
            GANHE A CADA GOL MARCADO</div>
    </div>
</div>
<div id="slider-artilheiros" class="flex flex-row gap-8 p-8 overflow-scroll" style="
  background-image: url('https://next-minio-b2b.devcore.at/next-cms/OyMF4D9xrF87q8wi_bKACA.png');
  background-size: cover;
  background-position: center;
  width: 100%;
  gap:32px;
"></div>
<div class="flex flex-col justify-center items-center w-full flex-grow overflow-x-clip bg-contentMain p-4">
    <div id="page-container"
        class="flex flex-col flex-grow shrink-0 w-full relative min-h-[calc(var(--doc-height)-152px)]">
        <div class="account-page-wrapper-helptext text-sm mb-4">
            <div class="account-section py-4">
                <div id="promo-details" class="account-section flex flex-col space-y-2 py-4">
                    <span class="account-section-title text-xl font-semibold mt-2 font-semibold" height="70px">Detalhes
                        da Promoção</span>
                    <div id="accountTop" class="account-section-items grid grid-cols-2 md:grid-cols-1 md:gap-1">
                        <div class="flex flex-col py-4" style="gap: 16px;">
                            <div
                                class="account-section-item flex items-stretch overflow-hidden bg-card first:rounded-t-md last:rounded-b-md first:border-t-[1px] last:border-b-[1px] border-x-[1px] md:rounded-md md:border-[1px] border-neutral light-green-bg">
                                <div class="account-section-item-icon flex justify-center items-center w-14">
                                    <div class="account-section-item-icon flex justify-center items-center w-14">
                                        <svg width="37" height="32" viewBox="0 0 37 32" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M18.8235 7.52941H3.76471C2.76624 7.52941 1.80868 7.92605 1.10266 8.63207C0.396637 9.33809 0 10.2957 0 11.2941V18.8235C0 19.822 0.396637 20.7796 1.10266 21.4856C1.80868 22.1916 2.76624 22.5882 3.76471 22.5882H5.64706V30.1176C5.64706 30.6169 5.84538 31.0957 6.19839 31.4487C6.5514 31.8017 7.03018 32 7.52941 32H11.2941C11.7933 32 12.2721 31.8017 12.6251 31.4487C12.9782 31.0957 13.1765 30.6169 13.1765 30.1176V22.5882H18.8235L28.2353 30.1176V0L18.8235 7.52941ZM36.7059 15.0588C36.7059 18.2776 34.8988 21.1953 32 22.5882V7.52941C34.88 8.94118 36.7059 11.8588 36.7059 15.0588Z"
                                                fill="#006E4E" />
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="account-section-item-details flex flex-col flex-grow overflow-hidden py-4 border-b-[.5px] border-neutral md:border-none">
                                    <span
                                        class="account-section-item-details-description overflow-hidden font-bold">Tipo
                                        de Promoção:</span><span class="account-section-item-details-title">Aposta
                                        esportiva</span>
                                </div>
                            </div>
                            <div
                                class="account-section-item flex items-stretch overflow-hidden bg-card first:rounded-t-md last:rounded-b-md first:border-t-[1px] last:border-b-[1px] border-x-[1px] md:rounded-md md:border-[1px] border-neutral light-green-bg">
                                <div class="account-section-item-icon flex justify-center items-center w-14">
                                    <div class="account-section-item-icon flex justify-center items-center w-14">
                                        <svg width="26" height="32" viewBox="0 0 26 32" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M11 4H7C6.08075 4 5.1705 4.18106 4.32122 4.53284C3.47194 4.88463 2.70026 5.40024 2.05025 6.05025C1.40024 6.70026 0.884626 7.47194 0.532843 8.32122C0.18106 9.1705 0 10.0807 0 11C0 11.9193 0.18106 12.8295 0.532843 13.6788C0.884626 14.5281 1.40024 15.2997 2.05025 15.9497C2.70026 16.5998 3.47194 17.1154 4.32122 17.4672C5.1705 17.8189 6.08075 18 7 18H11V24H2V28H11V32H15V28H19C20.8565 28 22.637 27.2625 23.9497 25.9497C25.2625 24.637 26 22.8565 26 21C26 19.1435 25.2625 17.363 23.9497 16.0503C22.637 14.7375 20.8565 14 19 14H15V8H24V4H15V0H11V4ZM15 18H19C19.7956 18 20.5587 18.3161 21.1213 18.8787C21.6839 19.4413 22 20.2044 22 21C22 21.7956 21.6839 22.5587 21.1213 23.1213C20.5587 23.6839 19.7956 24 19 24H15V18ZM11 14H7C6.60603 14 6.21593 13.9224 5.85195 13.7716C5.48797 13.6209 5.15726 13.3999 4.87868 13.1213C4.6001 12.8427 4.37913 12.512 4.22836 12.1481C4.0776 11.7841 4 11.394 4 11C4 10.606 4.0776 10.2159 4.22836 9.85195C4.37913 9.48797 4.6001 9.15726 4.87868 8.87868C5.15726 8.6001 5.48797 8.37913 5.85195 8.22836C6.21593 8.0776 6.60603 8 7 8H11V14Z"
                                                fill="#006E4E" />
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="account-section-item-details flex flex-col flex-grow overflow-hidden py-4 border-b-[.5px] border-neutral md:border-none">
                                    <span
                                        class="account-section-item-details-description overflow-hidden font-bold">Benefícios:</span><span
                                        class="account-section-item-details-title">R$5 a cada chute no gol</span>
                                </div>
                            </div>
                            <div
                                class="account-section-item flex items-stretch overflow-hidden bg-card first:rounded-t-md last:rounded-b-md first:border-t-[1px] last:border-b-[1px] border-x-[1px] md:rounded-md md:border-[1px] border-neutral light-green-bg">
                                <div class="account-section-item-icon flex justify-center items-center w-14">
                                    <div class="account-section-item-icon flex justify-center items-center w-14">
                                        <svg width="34" height="33" viewBox="0 0 34 33" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M0 11.1053C0 7.92883 -1.00387e-07 6.3423 0.986947 5.35536C1.97389 4.36841 3.56042 4.36841 6.73684 4.36841H26.9474C30.1238 4.36841 31.7103 4.36841 32.6973 5.35536C33.6842 6.3423 33.6842 7.92883 33.6842 11.1053C33.6842 11.8985 33.6842 12.296 33.4383 12.5436C33.1907 12.7895 32.7916 12.7895 32 12.7895H1.68421C0.890947 12.7895 0.493474 12.7895 0.245895 12.5436C-1.5058e-07 12.296 0 11.8968 0 11.1053ZM0 26.2631C0 29.4396 -1.00387e-07 31.0261 0.986947 32.013C1.97389 33 3.56042 33 6.73684 33H26.9474C30.1238 33 31.7103 33 32.6973 32.013C33.6842 31.0261 33.6842 29.4396 33.6842 26.2631V17.8421C33.6842 17.0488 33.6842 16.6514 33.4383 16.4038C33.1907 16.1579 32.7916 16.1579 32 16.1579H1.68421C0.890947 16.1579 0.493474 16.1579 0.245895 16.4038C-1.5058e-07 16.6514 0 17.0505 0 17.8421V26.2631Z"
                                                fill="#006E4E" />
                                            <path d="M8.42114 1V6.05263M25.2632 1V6.05263" stroke="#006E4E"
                                                stroke-width="2" stroke-linecap="round" />
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="account-section-item-details flex flex-col flex-grow overflow-hidden py-4 border-b-[.5px] border-neutral md:border-none">
                                    <span
                                        class="account-section-item-details-description text-primary overflow-hidden font-bold">Duração
                                        da Promoção:</span><span class="account-section-item-details-title">De
                                        <span class="account-section-item-details-title" id="init-day"></span> à
                                        <span class="account-section-item-details-title" id="end-day"></span> às
                                        16:00.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span class="account-section-title text-2xl font-semibold block mb-6">Como funciona?</span>
                <div class="account-section-items grid grid-cols-3">
                    <div class="account-section-item light-green-bg p-4 rounded shadow-md">
                        <div class="account-section-item-icon mb-4">
                            <span class="text-primary font-bold">Passo 1</span>
                        </div>
                        <div class="account-section-item-details">
                            <span class="block text-xl font-bold mb-2">🎯Escolha um artilheiro:</span>
                            <span class="block text-base">
                                Aposte a partir de R$10 em um dos artilheiros turbinados.
                            </span>
                        </div>
                    </div>
                    <div class="account-section-item light-green-bg p-4 rounded shadow-md">
                        <div class="account-section-item-icon mb-4">
                            <span class="text-primary font-bold">Passo 2</span>
                        </div>
                        <div class="account-section-item-details">
                            <span class="block text-xl font-bold mb-2">⚽Torça pelo gol:</span>
                            <span class="block text-base">
                                Se o jogador escolhido marcar gol, você ganha sua aposta normalmente.
                            </span>
                        </div>
                    </div>
                    <div class="account-section-item light-green-bg p-4 rounded shadow-md">
                        <div class="account-section-item-icon mb-4">
                            <span class="text-primary font-bold">Passo 3</span>
                        </div>
                        <div class="account-section-item-details">
                            <span class="block text-xl font-bold mb-2">🎁Bônus extra:</span>
                            <span class="block text-base">
                                A cada chute no gol do seu artilheiro, você recebe R$5 em aposta grátis, independentemente do resultado da aposta!
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <a id="user-id" class="hidden">{{user.id}}</a>
            <div id="jogo-responsavel" class="flex flex-col flex-start gap-3 py-4">
                <span class="account-section-title text-2xl font-semibold">Jogue com responsabilidade!</span>
                <p class="text-lg">Leia todas as informações disponíveis na nossa seção de <a
                        class="underline text-primary opacity-70 font-bold" href="/jogo-responsavel">Jogo
                        Responsável</a>.</p>
                <div class="account-section flex flex-row w-full">
                    <div class="w-full border border-1 border-neutral rounded-lg md-card light-green-bg">
                        <details>
                            <summary class="flex justify-between py-3 rounded-lg cursor-pointer">
                                <div class="flex flex-col items-center gap-4 w-full">
                                    <div class="flex gap-4 w-full">
                                        <div id="plus-icon" class="flex items-center ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-plus-circle">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="M8 12h8" />
                                                <path d="M12 8v8" />
                                            </svg>
                                        </div>
                                        <div id="minus-icon" class="hidden ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-minus-circle">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="M8 12h8" />
                                            </svg>
                                        </div>
                                        <div class="text-base font-semibold">
                                            Termos e Condições
                                        </div>
                                    </div>
                                </div>

                            </summary>

                            <div
                                class="flex flex-col rounded-md bg-gray-500 bg-opacity-10 p-2 mx-2 my-2 md:p-4 answer-body">
                                <p>
                                    1. Para participar, o cliente deve realizar uma aposta mínima de R$10,00 em uma das
                                    opções disponibilizadas.<br><br>

                                    2. Cada cliente pode escolher apenas um artilheiro para apostar, com limite máximo de R$50,00.<br><br>

                                    3. A cada chute no gol do artilheiro escolhido, o cliente receberá R$5,00 em
                                    aposta grátis.<br><br>

                                    4. A aposta grátis será creditada no dia seguinte (12/05/2025) até às 14:00.<br><br>

                                    5. O bônus será válido por 48 horas a partir do momento que foi creditado.<br><br>

                                    6. O bônus não possui rollover nem restrições para saque.<br><br>

                                    7. Apostas encerradas por meio da funcionalidade de cashout não serão elegíveis para
                                    a promoção.<br><br>

                                    8. Apostas realizadas com freebet (aposta grátis) não serão válidas para esta
                                    promoção.<br><br>

                                    9. Apostas canceladas por qualquer motivo não serão consideradas.<br><br>

                                    10. É considerado chute a gol uma tentativa intencional de marcar no gol adversário que:<br><br>
                                    10.1 Entra na rede e resulta em um gol.</p>
                                    10.2 É uma tentativa clara de marcar, que teria entrado no gol, mas foi defendida pelo goleiro. </p>  
                                    10.3 É uma tentativa clara de marcar, que teria entrado no gol, mas foi bloqueada por um jogador defensor que é o último jogador dentro do gol. </p>
                                    10.4 É empurrado para a trave ou travessão pelo goleiro após uma defesa.<br><br>

                                    11. O mercado de "marcador a qualquer momento" não considera gol contra.<br><br>

                                    12. O bônus recebido não é válido para apostas em cassino.<br><br>

                                    13. A Casa de Apostas reserva-se o direito de alterar ou suspender a promoção a
                                    qualquer momento, sem aviso prévio.<br><br>
                                </p>
                            </div>

                        </details>
                    </div>
                </div>
                <div class="account-section flex flex-row w-full">
                    <div class="w-full border border-1 border-neutral rounded-lg md-card light-green-bg">
                        <details>
                            <summary class="flex justify-between p-3 rounded-lg cursor-pointer">
                                <div class="flex flex-col items-center gap-4 w-full">
                                    <div class="flex gap-4 w-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="40px" height="40px" viewBox="0 0 40 50" fill="none">
                                            <path
                                                d="M8.88889 40V35.5556H17.7778V28.6667C15.963 28.2593 14.343 27.4911 12.9178 26.3622C11.4926 25.2333 10.4459 23.8163 9.77778 22.1111C7 21.7778 4.6763 20.5652 2.80667 18.4733C0.937037 16.3815 0.******** 13.9274 0 11.1111V8.88889C0 7.66667 0.435556 6.62074 1.30667 5.75111C2.17778 4.88148 3.2237 4.44593 4.44444 4.44444H8.88889V0H31.1111V4.44444H35.5556C36.7778 4.44444 37.8244 4.88 38.6956 5.75111C39.5667 6.62222 40.0015 7.66815 40 8.88889V11.1111C40 13.9259 39.0644 16.38 37.1933 18.4733C35.3222 20.5667 32.9985 21.7793 30.2222 22.1111C29.5556 23.8148 28.5096 25.2319 27.0844 26.3622C25.6593 27.4926 24.0385 28.2607 22.2222 28.6667V35.5556H31.1111V40H8.88889ZM8.88889 17.3333V8.88889H4.44444V11.1111C4.44444 12.5185 4.85185 13.7874 5.66667 14.9178C6.48148 16.0481 7.55555 16.8533 8.88889 17.3333ZM31.1111 17.3333C32.4444 16.8519 33.5185 16.0459 34.3333 14.9156C35.1481 13.7852 35.5556 12.517 35.5556 11.1111V8.88889H31.1111V17.3333Z"
                                                fill="#006E4E" />
                                        </svg>
                                        <div class="text-base font-semibold">
                                            Veja os resultados da última rodada!
                                        </div>
                                    </div>
                                </div>

                            </summary>

                            <div id="slider-results-artilheiros"
                                class="flex flex-row rounded-md bg-gray-500 bg-opacity-10 p-2 mx-2 my-2 md:p-4 answer-body overflow-scroll gap-4">
                            </div>

                        </details>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
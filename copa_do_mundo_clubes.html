<!DOCTYPE html>
<html lang="pt-br">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Copa do Mundo de Clubes 2025 | Casa de Apostas</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'custom-dark': '#091D28'
          },
          fontFamily: {
            'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
          }
        },
        fontFamily: {
          'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
        }
      }
    }
  </script>
  <style>
    /* Global Font Settings */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    body,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    span,
    a,
    button,
    input,
    select,
    textarea {
      font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }

    #copa-do-mundo-clubes-page {
        padding: 0;
    }

    #view-content>div {
      max-width: 100%;
    }

    body {
      font-family: 'Inter', ui-sans-serif, system-ui;
    }

    /* Scrollbar customization */
    .scrollbar-hide {
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;
      /* Firefox */
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
      /* Chrome, Safari, Opera */
    }

    /* Snap scrolling */
    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory>* {
      scroll-snap-align: center;
    }

    /* Hero Section Mobile-First */
    .hero-banner {
      width: 100%;
      height: auto;
      display: block;
      position: relative;
    }

    .hero-container {
      position: relative;
      width: 100%;
      overflow: hidden;
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    }

    .hero-image {
      width: 100%;
      height: 250px;
      object-fit: cover;
      object-position: center;
      transition: opacity 0.3s ease;
    }

    @media (min-width: 640px) {
      .hero-image {
        height: 350px;
      }
    }

    @media (min-width: 768px) {
      .hero-image {
        height: 400px;
      }
    }

    @media (min-width: 1024px) {
      .hero-image {
        height: 500px;
      }
    }

    @media (min-width: 1440px) {
      .hero-image {
        height: 600px;
      }
    }

    /* Overlay Content */
    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 1rem;
    }

    .hero-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: white;
      margin-bottom: 0.5rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .hero-subtitle {
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 1rem;
      max-width: 280px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    @media (min-width: 640px) {
      .hero-title {
        font-size: 2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
        max-width: 400px;
      }
    }

    @media (min-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.125rem;
        max-width: 500px;
      }
    }

    @media (min-width: 1024px) {
      .hero-title {
        font-size: 3rem;
      }

      .hero-subtitle {
        font-size: 1.25rem;
        max-width: 600px;
      }
    }

    /* CTA Button */
    .hero-cta {
      background: linear-gradient(45deg, #10b981, #34d399);
      color: white;
      font-weight: bold;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 2px solid transparent;
    }

    .hero-cta:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
      border-color: white;
    }

    @media (min-width: 640px) {
      .hero-cta {
        padding: 1rem 2rem;
        font-size: 1.125rem;
      }
    }

    /* Loading animations */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Scroll indicators */
    .scroll-indicator {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 12px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .drag-scroll:hover .scroll-indicator {
      opacity: 1;
    }

    /* Existing styles */
    .strikethrough {
      position: relative;
      font-weight: bold;
    }

    .strikethrough:before {
      position: absolute;
      content: "";
      left: 0;
      top: 50%;
      right: 0;
      border-top: 1.5px solid;
      border-color: #000;
      transform: rotate(-25deg);
      color: #000;
    }

    .slider-container {
      overflow: hidden;
      position: relative;
    }

    .slider {
      display: flex;
      backface-visibility: hidden;
      transform: translate3d(0, 0, 0);
    }

    #slider-copa {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
    }

    #slider-copa::-webkit-scrollbar {
      display: none;
    }

    #slider-copa>div {
      scroll-snap-align: start;
      flex-shrink: 0;
    }

    /* Enhanced scrollbar hiding and snap scroll for Odds cards */
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
      -webkit-overflow-scrolling: touch;
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }

    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory {
      scroll-snap-type: x mandatory;
    }

    .snap-start {
      scroll-snap-align: start;
    }

    /* Improved mobile card responsive behavior */
    .odds-card {
      scroll-snap-align: start;
      min-width: 280px;
    }

    @media (max-width: 640px) {
      .odds-card {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
      }
    }

    /* Countdown Timer Styles */
    .countdown-container {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      margin: 2rem 0;
    }

    .countdown-box {
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
      color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      text-align: center;
      min-width: 80px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .countdown-number {
      font-size: 1.5rem;
      font-weight: bold;
      display: block;
    }

    .countdown-label {
      font-size: 0.75rem;
      text-transform: uppercase;
      opacity: 0.8;
    }

    @media (min-width: 640px) {
      .countdown-box {
        min-width: 100px;
        padding: 1.5rem;
      }

      .countdown-number {
        font-size: 2rem;
      }

      .countdown-label {
        font-size: 0.875rem;
      }
    }

    /* Enhanced Animation for Banner */
    .animate-scroll {
      animation: scroll-left 80s linear infinite;
    }

    @keyframes scroll-left {
      0% {
        transform: translateX(0%);
      }

      100% {
        transform: translateX(-50%);
      }
    }

    /* Interactive hover effects */
    .fixture-card {
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .fixture-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .top-scorer-card {
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .top-scorer-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .top-scorer-card:hover::before {
      left: 100%;
    }

    .top-scorer-card:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }    /* ODDS TURBINADAS SECTION CUSTOM BG */
    .odds-turbinadas-section {
      background: #000000 !important;
      background-color: #000000 !important;
      background-image: none !important;
    }

    /* RESPONSIVE BANNER IMAGE */
    .banner-image {
      width: 100%;
      height: auto;
      object-fit: cover;
      object-position: center center;
      display: block;
    }

    /* Mobile - Manter seção central visível */
    @media (max-width: 640px) {
      .banner-image {
        height: 250px;
        object-position: center 30%;
      }
    }

    /* Tablet - Ajustar posicionamento */
    @media (min-width: 641px) and (max-width: 1024px) {
      .banner-image {
        height: 350px;
        object-position: center 35%;
      }
    }

    /* Desktop - Mostrar imagem completa */
    @media (min-width: 1025px) {
      .banner-image {
        height: auto;
        object-position: center center;
      }
    }    /* Telas muito pequenas - Foco na área central */
    @media (max-width: 480px) {
      .banner-image {
        height: 200px;
        object-position: center 25%;
      }
    }    /* AJUSTE ESPECÍFICO PARA TEXTO EM TELAS < 720px */
    @media (max-width: 720px) {
      #copy-bannet-text {
        bottom: 0.25rem !important; /* Equivale a bottom-1 */
      }
    }

    /* Telas muito pequenas - posicionamento com top */
    @media (max-width: 480px) {
      #copy-bannet-text {
        top: 12rem !important;
        bottom: auto !important;
      }
    }
  </style>
</head>

<body class="bg-gray-100 min-h-screen">

<!-- Banner and Hero section -->
<div class="w-full relative">
    <img src="https://staging-minio.devcore.at/next-cms/HJRzESNqHeRLhFeIYxV6uA.png" sizes="100vw"
      class="banner-image" alt="Copa do Mundo de Clubes 2025 - Banner Principal" loading="eager">      <div id="copy-bannet-text" class="absolute bottom-2 sm:bottom-4 md:bottom-5 lg:bottom-6 xl:bottom-1 left-0 right-0 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20">
        <div class="flex flex-col justify-start items-start gap-1 sm:gap-2 w-full max-w-4xl">
        <div class="text-amber-300 text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-extrabold font-['Inter'] uppercase leading-tight">Copa do Mundo de Clubes da fifa</div>
        <div class="text-white text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium font-['Inter'] leading-relaxed max-w-3xl lg:max-w-none">Acompanhe tudo do mundial e aproveite as melhores Odds na Casa!</div>
    </div>
</div>
</div>

  <!-- Odds Turbinadas Section -->
  <div class="w-full px-4 sm:px-6 md:px-20 pt-12 pb-14 bg-gradient-to-b from-stone-900 to-stone-950 odds-turbinadas-section">
    <div class="max-w-7xl mx-auto">
      <div class="flex flex-col gap-8">
        <div class="flex flex-col gap-4">
                    <!-- Special Markets -->
          <div class="self-stretch pr-4 inline-flex flex-col justify-start items-start gap-3">
            <h2 class="text-[#E9C043] text-2xl font-semibold">Mercados especiais</h2>
              <p class="text-white text-base"> As melhores odds para você sentir a emoção do mundial a cada lance.</p>  

            <div class="flex items-center gap-2">              
              <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                    fill="white" />
                </svg>
                <p style="padding-left: 0.5rem;">Deslize para o lado e explore mais mercados. </p>
              </div>
              
            </div>
            <div class="w-full h-full rounded-lg" style="border-radius: 100px">
              <cms-widget-ref widget-id="1131" widget-identifier="copa-do-mundo-hotbet" ></cms-widget-ref>
            </div>
          <div class="flex flex-col gap-1">
            <h2 class="text-[#E9C043] text-2xl font-semibold">Odds Turbinadas</h2>
            <p class="text-white text-base">
              Aposte em mercados selecionados e potencialize seus ganhos com a super odd da Casa!</p>
          </div>
          <div class="flex items-center gap-2">            
            <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                  fill="white" />
              </svg>
              <p style="padding-left: 0.5rem;">Deslize para o lado e veja mais SuperOdds</p>
            </div>
          </div>

          <!-- Cards Container -->
          <div class="w-full overflow-x-auto scrollbar-hide odds-scroll" style="cursor: grab;">
            <div class="flex gap-4 pb-4 snap-x snap-mandatory" style="min-width: max-content;">

              <!-- Card 1: Real Madrid vs Bayern -->
              <div class="odds-card flex-shrink-0 w-80 sm:w-96 snap-start">
                <div class="h-56 bg-white rounded-lg p-4 flex flex-col justify-between">
                  <!-- Header -->
                  <div class="flex justify-between items-center opacity-80">
                    <div class="flex items-center gap-2">
                      <div class="p-1 bg-[#E9C043] rounded-sm">
                        <div class="w-3 h-3 bg-white rounded"></div>
                      </div>
                      <span class="text-black text-xs font-medium">Real Madrid - Bayern</span>
                    </div>
                    <div>
                      <span class="text-black text-[10px] opacity-80">15/06/2025 às 16:00</span>
                    </div>
                  </div>

                  <!-- Betting Options -->
                  <div class="flex-1 flex flex-col gap-2 py-2">
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Marcador a qualquer momento</div>
                        <div class="text-black text-[10px]">Vinicius Jr</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Resultado</div>
                        <div class="text-black text-[10px]">Real Madrid</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Total de gols</div>
                        <div class="text-black text-[10px]">Mais de 2.5</div>
                      </div>
                    </div>
                  </div>

                  <!-- Odds Button -->
                  <div class="w-full">
                    <div class="h-8"
                      style="background:#006E4E; transition:background 0.2s; border-radius:0.375rem; display:flex; align-items:center; justify-content:center; padding-left:0.5rem; padding-right:0.5rem; cursor:pointer;"
                      onmouseover="this.style.background='#00523A'" onmouseout="this.style.background='#006E4E'">
                      <span class="opacity-80 text-white text-xs font-normal line-through mr-2">3.25</span>
                      <span class="text-white text-base font-bold">→ 4.50</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Card 2: Fluminense vs Al-Hilal -->
              <div class="odds-card flex-shrink-0 w-80 sm:w-96 snap-start">
                <div class="h-56 bg-white rounded-lg p-4 flex flex-col justify-between">
                  <!-- Header -->
                  <div class="flex justify-between items-center opacity-80">
                    <div class="flex items-center gap-2">
                      <div class="p-1 bg-[#E9C043] rounded-sm">
                        <div class="w-3 h-3 bg-white rounded"></div>
                      </div>
                      <span class="text-black text-xs font-medium">Fluminense - Al-Hilal</span>
                    </div>
                    <div>
                      <span class="text-black text-[10px] opacity-80">17/06/2025 às 19:00</span>
                    </div>
                  </div>

                  <!-- Betting Options -->
                  <div class="flex-1 flex flex-col gap-2 py-2">
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Marcador a qualquer momento</div>
                        <div class="text-black text-[10px]">Cano</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Ambas as equipes marcam</div>
                        <div class="text-black text-[10px]">Sim</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Resultado</div>
                        <div class="text-black text-[10px]">Fluminense</div>
                      </div>
                    </div>
                  </div>

                  <!-- Odds Button -->
                  <div class="w-full">
                    <div class="h-8"
                      style="background:#006E4E; transition:background 0.2s; border-radius:0.375rem; display:flex; align-items:center; justify-content:center; padding-left:0.5rem; padding-right:0.5rem; cursor:pointer;"
                      onmouseover="this.style.background='#00523A'" onmouseout="this.style.background='#006E4E'">
                      <span class="opacity-80 text-white text-xs font-normal line-through mr-2">3.80</span>
                      <span class="text-white text-base font-bold">→ 4.95</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Card 3: Manchester City vs Inter -->
              <div class="odds-card flex-shrink-0 w-80 sm:w-96 snap-start">
                <div class="h-56 bg-white rounded-lg p-4 flex flex-col justify-between">
                  <!-- Header -->
                  <div class="flex justify-between items-center opacity-80">
                    <div class="flex items-center gap-2">
                      <div class="p-1 bg-[#E9C043] rounded-sm">
                        <div class="w-3 h-3 bg-white rounded"></div>
                      </div>
                      <span class="text-black text-xs font-medium">Man City - Inter</span>
                    </div>
                    <div>
                      <span class="text-black text-[10px] opacity-80">16/06/2025 às 21:00</span>
                    </div>
                  </div>

                  <!-- Betting Options -->
                  <div class="flex-1 flex flex-col gap-2 py-2">
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Chutes no gol</div>
                        <div class="text-black text-[10px]">Haaland 2+</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Marcador a qualquer momento</div>
                        <div class="text-black text-[10px]">Haaland</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Total de gols</div>
                        <div class="text-black text-[10px]">Mais de 2.5</div>
                      </div>
                    </div>
                  </div>

                  <!-- Odds Button -->
                  <div class="w-full">
                    <div class="h-8"
                      style="background:#006E4E; transition:background 0.2s; border-radius:0.375rem; display:flex; align-items:center; justify-content:center; padding-left:0.5rem; padding-right:0.5rem; cursor:pointer;"
                      onmouseover="this.style.background='#00523A'" onmouseout="this.style.background='#006E4E'">
                      <span class="opacity-80 text-white text-xs font-normal line-through mr-2">3.90</span>
                      <span class="text-white text-base font-bold">→ 5.20</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Card 4: Palmeiras vs Borussia Dortmund -->
              <div class="odds-card flex-shrink-0 w-80 sm:w-96 snap-start">
                <div class="h-56 bg-white rounded-lg p-4 flex flex-col justify-between">
                  <!-- Header -->
                  <div class="flex justify-between items-center opacity-80">
                    <div class="flex items-center gap-2">
                      <div class="p-1 bg-[#E9C043] rounded-sm">
                        <div class="w-3 h-3 bg-white rounded"></div>
                      </div>
                      <span class="text-black text-xs font-medium">Palmeiras - Dortmund</span>
                    </div>
                    <div>
                      <span class="text-black text-[10px] opacity-80">18/06/2025 às 16:00</span>
                    </div>
                  </div>

                  <!-- Betting Options -->
                  <div class="flex-1 flex flex-col gap-2 py-2">
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Marcador a qualquer momento</div>
                        <div class="text-black text-[10px]">Estêvão</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Resultado</div>
                        <div class="text-black text-[10px]">Palmeiras</div>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-neutral-300 rounded-full border-2 border-zinc-500/10"></div>
                      <div class="flex-1 border-l-2 border-zinc-500/10 pl-3">
                        <div class="text-black text-xs font-medium">Ambas marcam e mais de 2.5 gols</div>
                        <div class="text-black text-[10px]">Sim</div>
                      </div>
                    </div>
                  </div>

                  <!-- Odds Button -->
                  <div class="w-full">
                    <div class="h-8"
                      style="background:#006E4E; transition:background 0.2s; border-radius:0.375rem; display:flex; align-items:center; justify-content:center; padding-left:0.5rem; padding-right:0.5rem; cursor:pointer;"
                      onmouseover="this.style.background='#00523A'" onmouseout="this.style.background='#006E4E'">
                      <span class="opacity-80 text-white text-xs font-normal line-through mr-2">4.20</span>
                      <span class="text-white text-base font-bold">→ 5.40</span>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>


          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Animated Banner Section -->
  <div class="w-full bg-[#E9C043] py-4 overflow-hidden">
    <div id="slider-motion" class="flex whitespace-nowrap">
      <div
        class="flex items-center justify-start text-[#070300] text-base font-bold font-sans uppercase leading-relaxed whitespace-nowrap animate-scroll">
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <!-- Duplicação do conteúdo para preencher todo o espaço -->
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
      </div>
    </div>
  </div>
  <!-- Standings Group Selector and Table -->
  <div id="standings-section" class="w-full max-w-3xl mx-auto mt-8 mb-8 px-4">
    <div class="flex flex-col gap-4">

      <div class="self-stretch px-4 inline-flex flex-col justify-start items-start gap-4">
        <div class="self-stretch pt-2 pb-3 border-b border-black inline-flex justify-center items-center gap-10">
          <div
            class="w-6 h-6 origin-top-left rotate-180 inline-flex flex-col justify-center items-center overflow-hidden">
            <div data-variant="36" class="w-6 h-6 relative overflow-hidden">
              <div class="w-2 h-3 left-[8px] top-[5.75px] absolute bg-neutral-400"></div>
              <div class="w-6 h-6 left-0 top-[-0.25px] absolute"></div>
            </div>
          </div>
          <div class="flex-1 h-4 text-center justify-center text-black text-sm font-semibold font-sans">FASE DE GRUPOS
          </div>
          <div class="w-6 h-6 inline-flex flex-col justify-center items-center overflow-hidden">
            <div data-variant="36" class="w-6 h-6 relative overflow-hidden">
              <div class="w-2 h-3 left-[8.59px] top-[5.75px] absolute bg-green-600"></div>
              <div class="w-6 h-6 left-0 top-[-0.25px] absolute"></div>
            </div>
          </div>
        </div>
        <div class="self-stretch text-center justify-center text-black text-sm font-normal font-sans">32 times divididos
          em 8 grupos de 4. Os dois melhores de cada grupo avançam para a próxima fase.</div>
        <div class="self-stretch inline-flex justify-center items-center gap-2">
          <div class="flex-1 max-w-5 flex justify-end items-center gap-2">
          </div>
          <div class="self-stretch inline-flex justify-center text-black text-sm font-normal font-sans">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                fill="black" />
            </svg>
            <p style="padding-left: 0.5rem;">Toque e selecione um grupo</p>
          </div>
        </div>
      </div>

      <!-- Group Selector -->
      <div
        class="flex flex-col sm:flex-row items-center justify-between gap-4 bg-white rounded-lg shadow p-4 border border-gray-200">
        <label for="group-select" class="text-black text-base font-semibold font-sans">Selecione um grupo:</label>
        <select id="group-select"
          class="w-full sm:w-60 px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-700 text-black font-medium bg-gray-50 transition">
        </select>
      </div>
      <div id="standings-groups"></div>
    </div>
  </div>


<!-- Animated Banner Section -->
  <div class="w-full bg-[#E9C043] py-4 overflow-hidden">
    <div id="slider-motion" class="flex whitespace-nowrap">
      <div
        class="flex items-center justify-start text-[#070300] text-base font-bold font-sans uppercase leading-relaxed whitespace-nowrap animate-scroll">
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <!-- Duplicação do conteúdo para preencher todo o espaço -->
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ESCOLHA SEU FAVORITO</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">ABRA SUA CONTA E APOSTE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
      </div>
    </div>
  </div> 

  <!-- Topscore rankings-->
  <div class="self-stretch py-14 bg-gradient-to-b from-stone-900 to-stone-950 flex flex-col gap-8">
    <div class="self-stretch px-4 flex flex-col gap-3">      
      <div class="text-amber-300 text-3xl font-extrabold font-sans text-center">Artilheiros da Copa</div>
      <div class="self-stretch justify-center text-white text-base font-normal font-sans text-center">
        Quem será o goleador do Mundial? Acompanhe a corrida pela artilharia e as maiores promessas do torneio.
      </div>
      <div class="flex justify-center items-center gap-1">
        <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
           <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                  fill="white" />
              </svg>
              <p style="padding-left: 0.5rem;">Deslize para o lado e confira o ranking!</p>
        </div>
      </div>    </div>    
    <div class="self-stretch h-10 py-2.5 border-t border-b border-amber-500/30 inline-flex justify-between items-center gap-2 px-8">
      <div class="text-amber-300 text-base font-semibold font-sans uppercase leading-tight">Ranking</div>
      <div class="text-amber-300 text-base font-semibold font-sans uppercase leading-tight">Gols</div>
    </div>
    <div class="w-full px-4 flex flex-col items-center">
      <div id="artilheiros-copa" class="w-full flex gap-4 overflow-x-auto scrollbar-hide pb-4 snap-x snap-mandatory"></div>
    </div>
  </div>

  <!-- Responsibility Game -->
  <div class="self-stretch py-14 bg-gray-100 flex flex-col gap-8">
    <div class="self-stretch px-4 flex flex-col gap-3">
      <div class="text-slate-900 text-3xl font-extrabold font-sans text-center">Jogue com responsabilidade!</div>
      <div class="self-stretch justify-center text-slate-700 text-base font-normal font-sans text-center">
        A emoção do mundial começa aqui! Acompanhe todos os jogos, confira estatísticas e aproveite odds turbinadas, tudo em um só lugar!
      </div>
      <div class="flex justify-center items-center gap-1">
        <div class="self-stretch inline-flex justify-center text-slate-700 text-sm font-normal font-sans"></div>
          <p style="padding-left: 0.5rem;">Leia todas as informações disponíveis na nossa seção de <span class="text-emerald-700 font-semibold underline">
            <a href="https://casadeapostas.bet.br/br/jogo-responsavel">Jogo Responsável</a></span>.</p>
        </div>
      </div>
    </div>
      <div class="w-full px-4 flex flex-col items-center">
      <div class="w-full max-w-4xl">
        <!-- Expandable Terms Section -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <button 
            id="terms-toggle" 
            class="w-full px-4 py-3 bg-white hover:bg-gray-50 transition-colors flex items-center justify-between cursor-pointer focus:outline-none"
            onclick="toggleTerms()"
          >
            <div class="flex items-center gap-2">             
              <span>Clique aqui para ler os termos e condições</span>
            </div>
            <div class="flex items-center">
              <span id="terms-icon" class="text-xl font-bold text-gray-600">+</span>
              <svg id="chevron-icon" class="w-5 h-5 ml-2 text-gray-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          
          <!-- Terms Content (Initially Hidden) -->
          <div id="terms-content" class="hidden px-4 pb-4 bg-gray-50 border-t">
            <div class="py-4 text-sm text-gray-700">
              <h3 class="font-bold text-lg mb-3 text-gray-900">Termos e Condições - Copa do Mundo de Clubes</h3>
              
              <div class="space-y-3">
                <p><strong>1. Elegibilidade:</strong> Esta promoção é válida apenas para usuários maiores de 18 anos e residentes no Brasil.</p>
                
                <p><strong>2. Período de Validade:</strong> A promoção é válida durante todo o período da Copa do Mundo de Clubes 2025.</p>
                
                <p><strong>3. Odds Turbinadas:</strong> As odds especiais são aplicadas automaticamente em mercados selecionados e podem variar conforme a disponibilidade.</p>
                
                <p><strong>4. Limites de Aposta:</strong> Cada usuário pode fazer no máximo 5 apostas por dia com odds turbinadas, respeitando o limite máximo de R$ 500,00 por aposta.</p>
                
                <p><strong>5. Pagamento de Prêmios:</strong> Os prêmios serão creditados automaticamente na conta do usuário em até 24 horas após a confirmação do resultado.</p>
                
                <p><strong>6. Cancelamento:</strong> A Casa de Apostas reserva-se o direito de cancelar apostas suspeitas de fraude ou manipulação.</p>
                
                <p><strong>7. Jogo Responsável:</strong> Apostas devem ser feitas com responsabilidade. Em caso de vício em jogos, procure ajuda profissional.</p>
                
                <p><strong>8. Alterações:</strong> Estes termos podem ser alterados a qualquer momento, com notificação prévia aos usuários.</p>
                
                <p><strong>9. Contato:</strong> Para dúvidas ou suporte, entre em contato através do nosso chat online ou e-mail: <EMAIL></p>
                
                <p class="text-xs text-gray-500 mt-4">Última atualização: Janeiro 2025</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>  <script>
    function toggleTerms() {
      const content = document.getElementById('terms-content');
      const icon = document.getElementById('terms-icon');
      const chevron = document.getElementById('chevron-icon');
      
      if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.textContent = '−';
        chevron.style.transform = 'rotate(180deg)';
      } else {
        content.classList.add('hidden');
        icon.textContent = '+';
        chevron.style.transform = 'rotate(0deg)';
      }
    }
  </script>

 <!-- Renderização dinâmica dos artilheiros (top scorers) --->
  <script>    
      (function () {
        const artilheirosDiv = document.getElementById('artilheiros-copa');
        if (!artilheirosDiv) return;
        artilheirosDiv.innerHTML = '';
        (window.artilheiros || []).forEach((artilheiro, idx) => {
          artilheirosDiv.insertAdjacentHTML('beforeend', `
          <div class="flex-shrink-0 w-40 sm:w-48 md:w-56 rounded-lg bg-gradient-to-b from-neutral-900 to-neutral-800 shadow-lg overflow-hidden flex flex-col items-center justify-between border border-amber-500/20">
            <div class="flex flex-col items-center justify-center bg-gradient-to-r from-amber-600 to-amber-500 p-4 w-full">
              <div class="text-white text-2xl font-bold mb-2">#${idx + 1}</div>
              <img src="${artilheiro.logo}" alt="${artilheiro.name}" class="w-16 h-16 rounded-full border-2 border-white mb-2 object-cover shadow-md">
              <span class="block text-sm font-semibold text-white/80">${artilheiro.team}</span>
              <span class="block text-2xl font-bold text-white">${artilheiro.goals} <span class='text-base font-normal'>gols</span></span>
            </div>
            <div class="p-3 w-full text-center">
              <h4 class="text-base font-semibold text-amber-300">${artilheiro.name}</h4>
            </div>
          </div>
        `);
        });
      })();
  </script>

  <script>
    const STANDINGS_API_URL = 'https://v3.football.api-sports.io/standings?league=15&season=2025';
    const API_KEY = '6ab9fabfb32d18cad9adb9525d1076ac'; // Substitua pela sua chave da API-SPORTS
    let standingsGroupsData = [];    async function getStandings() {
      try {
        // Primeiro tenta buscar dados da API
        const response = await fetch(STANDINGS_API_URL, {
          method: 'GET',
          headers: {
            'x-apisports-key': API_KEY
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          if (data.response && data.response[0] && data.response[0].league.standings) {
            standingsGroupsData = data.response[0].league.standings;
            renderGroupSelect(standingsGroupsData);
            renderStandingsGroup(0);
            return;
          }
        }
      } catch (error) {
        console.warn('API request failed, using mock data:', error);
      }
      
      // Fallback para dados mockados se a API falhar
      useMockStandingsData();
    }
    
    function useMockStandingsData() {
      // Dados mockados da Copa do Mundo de Clubes
      const mockData = [
        // Grupo A
        [
          { rank: 1, team: { name: 'Al Ahly', logo: 'https://via.placeholder.com/24' }, points: 6, all: { played: 2, win: 2, draw: 0, lose: 0, goals: { for: 4, against: 1 } }, goalsDiff: 3, group: 'Grupo A' },
          { rank: 2, team: { name: 'Inter Miami', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 2, against: 2 } }, goalsDiff: 0, group: 'Grupo A' },
          { rank: 3, team: { name: 'Porto', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 1, against: 2 } }, goalsDiff: -1, group: 'Grupo A' },
          { rank: 4, team: { name: 'Palmeiras', logo: 'https://via.placeholder.com/24' }, points: 0, all: { played: 2, win: 0, draw: 0, lose: 2, goals: { for: 1, against: 3 } }, goalsDiff: -2, group: 'Grupo A' }
        ],
        // Grupo B
        [
          { rank: 1, team: { name: 'PSG', logo: 'https://via.placeholder.com/24' }, points: 6, all: { played: 2, win: 2, draw: 0, lose: 0, goals: { for: 5, against: 1 } }, goalsDiff: 4, group: 'Grupo B' },
          { rank: 2, team: { name: 'Atlético Madrid', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 3, against: 3 } }, goalsDiff: 0, group: 'Grupo B' },
          { rank: 3, team: { name: 'Botafogo', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 2, against: 3 } }, goalsDiff: -1, group: 'Grupo B' },
          { rank: 4, team: { name: 'Seattle Sounders', logo: 'https://via.placeholder.com/24' }, points: 0, all: { played: 2, win: 0, draw: 0, lose: 2, goals: { for: 1, against: 4 } }, goalsDiff: -3, group: 'Grupo B' }
        ],
        // Grupo C
        [
          { rank: 1, team: { name: 'Bayern München', logo: 'https://via.placeholder.com/24' }, points: 6, all: { played: 2, win: 2, draw: 0, lose: 0, goals: { for: 6, against: 2 } }, goalsDiff: 4, group: 'Grupo C' },
          { rank: 2, team: { name: 'Auckland City', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 2, against: 3 } }, goalsDiff: -1, group: 'Grupo C' },
          { rank: 3, team: { name: 'Boca Juniors', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 3, against: 4 } }, goalsDiff: -1, group: 'Grupo C' },
          { rank: 4, team: { name: 'Benfica', logo: 'https://via.placeholder.com/24' }, points: 0, all: { played: 2, win: 0, draw: 0, lose: 2, goals: { for: 2, against: 4 } }, goalsDiff: -2, group: 'Grupo C' }
        ],
        // Grupo D
        [
          { rank: 1, team: { name: 'Flamengo', logo: 'https://via.placeholder.com/24' }, points: 6, all: { played: 2, win: 2, draw: 0, lose: 0, goals: { for: 4, against: 1 } }, goalsDiff: 3, group: 'Grupo D' },
          { rank: 2, team: { name: 'Espérance', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 2, against: 2 } }, goalsDiff: 0, group: 'Grupo D' },
          { rank: 3, team: { name: 'Chelsea', logo: 'https://via.placeholder.com/24' }, points: 3, all: { played: 2, win: 1, draw: 0, lose: 1, goals: { for: 2, against: 3 } }, goalsDiff: -1, group: 'Grupo D' },
          { rank: 4, team: { name: 'León', logo: 'https://via.placeholder.com/24' }, points: 0, all: { played: 2, win: 0, draw: 0, lose: 2, goals: { for: 1, against: 3 } }, goalsDiff: -2, group: 'Grupo D' }
        ]
      ];
      
      standingsGroupsData = mockData;
      renderGroupSelect(standingsGroupsData);
      renderStandingsGroup(0);
    }function renderGroupSelect(groups) {
      const select = document.getElementById('group-select');
      if (!select) {
        console.error('Element with id "group-select" not found');
        return;
      }
      
      select.innerHTML = '';
      
      if (!groups || !Array.isArray(groups)) {
        console.error('Invalid groups data provided to renderGroupSelect');
        return;
      }
      
      groups.forEach((groupArr, idx) => {
        if (!groupArr || !groupArr.length) return;
        const groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
        const option = document.createElement('option');
        option.value = idx;
        option.textContent = groupName;
        select.appendChild(option);
      });
      
      select.onchange = function () {
        renderStandingsGroup(Number(this.value));
      };
    }

    function renderStandingsGroup(idx) {
      const container = document.getElementById('standings-groups');
      container.innerHTML = '';
      const groupArr = standingsGroupsData[idx];
      if (!groupArr || !groupArr.length) return;
      const groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
      let table = `<div class='overflow-x-auto rounded-lg border border-gray-200 bg-white shadow mt-4'>
        <h3 class='text-lg font-bold text-green-700 mb-2 px-4 pt-4'>${groupName}</h3>
        <table class='min-w-[600px] w-full bg-white rounded-lg'>
          <thead class='bg-gradient-to-r from-gray-50 to-gray-100'>
            <tr>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pos</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider text-left'>Time</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pts</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>J</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>V</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>E</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>D</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GP</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GC</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>SG</th>
            </tr>
          </thead>
          <tbody class='bg-white divide-y divide-gray-200'>`;
      groupArr.forEach(team => {
        table += `<tr class='hover:bg-green-50 transition'>
          <td class='px-2 py-1 text-center font-semibold'>${team.rank}</td>
          <td class='px-2 py-1 flex items-center gap-2'><img src='${team.team.logo}' alt='${team.team.name}' class='w-6 h-6 inline-block rounded-full border' />${team.team.name}</td>
          <td class='px-2 py-1 text-center font-bold text-green-700'>${team.points}</td>
          <td class='px-2 py-1 text-center'>${team.all.played}</td>
          <td class='px-2 py-1 text-center'>${team.all.win}</td>
          <td class='px-2 py-1 text-center'>${team.all.draw}</td>
          <td class='px-2 py-1 text-center'>${team.all.lose}</td>
          <td class='px-2 py-1 text-center'>${team.all.goals.for}</td>
          <td class='px-2 py-1 text-center'>${team.all.goals.against}</td>
          <td class='px-2 py-1 text-center'>${team.goalsDiff}</td>
        </tr>`;
      });
      table += '</tbody></table></div>';
      container.insertAdjacentHTML('beforeend', table);
    }

    getStandings();
  </script>

  <!-- ...Mock data ... -->
  <script>
    // Simulação de dados (substitua por fetch de API se desejar)
    const fixtures = [
      { date: '12/06/2025', home: 'Al Ahly', away: 'Auckland City', hour: '16:00' },
      { date: '15/06/2025', home: 'León', away: 'Urawa Reds', hour: '19:00' },
      { date: '18/06/2025', home: 'Manchester City', away: 'Al Ahly', hour: '16:00' },
      { date: '21/06/2025', home: 'Fluminense', away: 'León', hour: '19:00' },
      { date: '24/06/2025', home: 'Final', away: '', hour: '16:00' }
    ];
    const standings = [
      { rank: 1, name: 'Manchester City', logo: 'imgs/50.png', points: 6, played: 2, win: 2, draw: 0, lose: 0 },
      { rank: 2, name: 'Fluminense', logo: 'imgs/54.png', points: 3, played: 2, win: 1, draw: 0, lose: 1 },
      { rank: 3, name: 'León', logo: 'imgs/538.png', points: 3, played: 2, win: 1, draw: 0, lose: 1 },
      { rank: 4, name: 'Al Ahly', logo: 'imgs/193.png', points: 0, played: 2, win: 0, draw: 0, lose: 2 }];    // Dados dos artilheiros
    window.artilheiros = [
      { name: 'Erling Haaland', team: 'Manchester City', goals: 8, logo: 'https://cdn.sofifa.net/players/239/085/25_120.png' },
      { name: 'Kylian Mbappé', team: 'Real Madrid', goals: 7, logo: 'https://cdn.sofifa.net/players/231/747/25_120.png' },
      { name: 'Vinicius Jr', team: 'Real Madrid', goals: 6, logo: 'https://cdn.sofifa.net/players/238/794/25_120.png' },
      { name: 'Lionel Messi', team: 'Inter Miami', goals: 5, logo: 'https://cdn.sofifa.net/players/158/023/25_120.png' },
      { name: 'Germán Cano', team: 'Fluminense', goals: 4, logo: 'https://assets.goal.com/v3/assets/bltcc7a7ffd2fbf71f5/blt5e3e6e1c6b6c4f4a/60c5a5e6b7e7b5002c5e4f5a/german_cano.jpg' },
      { name: 'Mohamed Salah', team: 'Liverpool', goals: 4, logo: 'https://cdn.sofifa.net/players/209/331/25_120.png' },
      { name: 'Endrick', team: 'Real Madrid', goals: 3, logo: 'https://assets.goal.com/v3/assets/bltcc7a7ffd2fbf71f5/blt8b7e5c7a5f5c7a5f/64f5c7a5f5c7a5f5c7a5/endrick.jpg' },
      { name: 'Lewandowski', team: 'Barcelona', goals: 3, logo: 'https://cdn.sofifa.net/players/188/545/25_120.png' },
      { name: 'Lautaro Martínez', team: 'Internazionale', goals: 2, logo: 'https://cdn.sofifa.net/players/231/478/25_120.png' },
      { name: 'Julián Álvarez', team: 'Atlético Madrid', goals: 2, logo: 'https://cdn.sofifa.net/players/237/463/25_120.png' }
    ];// Countdown Timer Function
    function startCountdown() {
      const targetDate = new Date('2025-06-18T16:00:00-03:00'); // Next game date

      function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate.getTime() - now;

        if (distance < 0) {
          document.getElementById('days').textContent = '00';
          document.getElementById('hours').textContent = '00';
          document.getElementById('minutes').textContent = '00';
          document.getElementById('seconds').textContent = '00';
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        document.getElementById('days').textContent = String(days).padStart(2, '0');
        document.getElementById('hours').textContent = String(hours).padStart(2, '0');
        document.getElementById('minutes').textContent = String(minutes).padStart(2, '0');
        document.getElementById('seconds').textContent = String(seconds).padStart(2, '0');
      }

      updateCountdown();
      setInterval(updateCountdown, 1000);
    }    // Enhanced Banner Animation
    function initSliderMotion() {
      const slider = document.getElementById('slider-motion');
      if (!slider) return;

      // A animação agora é controlada principalmente pelo CSS
      // Isso garante que o slider seja inicializado corretamente
      const sliderContent = slider.querySelector('.animate-scroll');
      if (sliderContent) {
        // Garantir que a largura do conteúdo seja suficiente para cobrir toda a tela
        const viewportWidth = window.innerWidth;
        if (sliderContent.offsetWidth < viewportWidth * 2) {
          // Se o conteúdo não for largo o suficiente, ajustamos o CSS dinamicamente
          slider.style.minWidth = `${viewportWidth * 2}px`;
        }
      }
    }

    // Initialize drag scroll functionality
    function initDragScroll() {
      const scrollContainers = document.querySelectorAll('#slider-copa, #top-scorers-copa');
      scrollContainers.forEach(container => {
        if (!container) return;

        let isDown = false;
        let startX;
        let scrollLeft;

        container.addEventListener('mousedown', (e) => {
          isDown = true;
          container.style.cursor = 'grabbing';
          startX = e.pageX - container.offsetLeft;
          scrollLeft = container.scrollLeft;
        });

        container.addEventListener('mouseleave', () => {
          isDown = false;
          container.style.cursor = 'grab';
        });

        container.addEventListener('mouseup', () => {
          isDown = false;
          container.style.cursor = 'grab';
        });

        container.addEventListener('mousemove', (e) => {
          if (!isDown) return;
          e.preventDefault();
          const x = e.pageX - container.offsetLeft;
          const walk = (x - startX) * 2;
          container.scrollLeft = scrollLeft - walk;
        });
      });
    }

    // Render fixtures with enhanced styling
    const fixturesDiv = document.getElementById('fixtures-copa');
    fixtures.forEach(jogo => {
      fixturesDiv.insertAdjacentHTML('beforeend', `
        <div class="fixture-card flex justify-between items-center bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500 hover:border-blue-600 transition-all duration-300">
          <div class="flex flex-col">
            <span class="font-semibold text-gray-800">${jogo.date}</span>
            <span class="text-sm text-gray-500">${jogo.hour}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="font-medium text-gray-700">${jogo.home}</span>
            <span class="font-bold text-blue-700 px-2">VS</span>
            <span class="font-medium text-gray-700">${jogo.away}</span>
          </div>
          <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      `);
    });    // Render standings
    const standingsTbody = document.getElementById('standings-copa');
    if (standingsTbody) {
      standings.forEach(team => {
        standingsTbody.insertAdjacentHTML('beforeend', `<tr><td class="px-2 py-1">${team.rank}</td><td class="px-2 py-1 flex items-center gap-2"><img src="${team.logo}" alt="${team.name}" class="w-6 h-6 inline-block rounded-full border" />${team.name}</td><td class="px-2 py-1">${team.points}</td><td class="px-2 py-1">${team.played}</td><td class="px-2 py-1">${team.win}</td><td class="px-2 py-1">${team.draw}</td><td class="px-2 py-1">${team.lose}</td></tr>`);
      });
    }    // Inicializações
    document.addEventListener('DOMContentLoaded', () => {
      startCountdown();
      initSliderMotion();
      initDragScroll();
    });
  </script>

  <script>
    // Drag-to-scroll para odds turbinadas
    (function () {
      const oddsScroll = document.querySelector('.odds-scroll');
      let isDown = false;
      let startX;
      let scrollLeft;
      if (oddsScroll) {
        oddsScroll.addEventListener('mousedown', (e) => {
          isDown = true;
          oddsScroll.classList.add('active');
          startX = e.pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('mouseleave', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mouseup', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mousemove', (e) => {
          if (!isDown) return;
          e.preventDefault();
          const x = e.pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5; // scroll-fast
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
        // Touch support
        oddsScroll.addEventListener('touchstart', (e) => {
          isDown = true;
          startX = e.touches[0].pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('touchend', () => {
          isDown = false;
        });
        oddsScroll.addEventListener('touchmove', (e) => {
          if (!isDown) return;
          const x = e.touches[0].pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5;
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
      }
    })();
  </script>
 
 
 <!-- Betslip remover --->
  <script>
   document.addEventListener("DOMContentLoaded", () => {
  const observer = new MutationObserver(() => {
    const containerRight = document.querySelector(".view-widget-container-right");
    const betslipDesktop = document.querySelector(".betslip-desktop");

    if (containerRight && betslipDesktop) {
      // Move o betslip para o body ANTES de esconder o container lateral
      document.body.appendChild(betslipDesktop);

      // Esconde o container lateral (espaço em branco)
      containerRight.style.display = "none";

      // Ajusta o estilo do betslip
      betslipDesktop.style.position = "fixed";
      betslipDesktop.style.bottom = "0";
      betslipDesktop.style.right = "0";
      betslipDesktop.style.zIndex = "9999";
      betslipDesktop.style.maxHeight = "90vh";
      betslipDesktop.style.overflowY = "auto";
      betslipDesktop.style.display = "block";
      betslipDesktop.style.background = "white"; // opcional, se estiver transparente
      betslipDesktop.style.boxShadow = "0 0 10px rgba(0,0,0,0.3)"; // opcional

      observer.disconnect();
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
});


  </script>

</body>

</html>
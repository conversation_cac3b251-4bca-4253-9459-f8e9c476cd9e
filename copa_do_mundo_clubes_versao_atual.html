<!DOCTYPE html>
<html lang="pt-br">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Copa do Mundo de Clubes 2025 | Casa de Apostas</title>
  
  <!-- Tailwind CSS Config -->
  <script async src="https://cdn.tailwindcss.com">
  </script>

  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'custom-dark': '#091D28'
          },
          fontFamily: {
            'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
          }
        },
        fontFamily: {
          'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
        }
      }
    }
  </script>

  <!-- Estilos CSS -->
  <style>
    /* Global Font Settings */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    body,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    span,
    a,
    button,
    input,
    select,
    textarea {
      font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }

    #copa-do-mundo-clubes-2-page {
      padding: 0;
    }

    #view-content>div {
      max-width: 100%;
    }

    body {
      font-family: 'Inter', ui-sans-serif, system-ui;
    }

    /* Scrollbar customization */
    .scrollbar-hide {
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;
      /* Firefox */
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
      /* Chrome, Safari, Opera */
    }

    /* Snap scrolling */
    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory>* {
      scroll-snap-align: center;
    }

    /* Hero Section Mobile-First */
    .hero-banner {
      width: 100%;
      height: auto;
      display: block;
      position: relative;
    }

    .hero-container {
      position: relative;
      width: 100%;
      overflow: hidden;
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    }

    .hero-image {
      width: 100%;
      height: 250px;
      object-fit: cover;
      object-position: center;
      transition: opacity 0.3s ease;
    }

    @media (min-width: 640px) {
      .hero-image {
        height: 350px;
      }
    }

    @media (min-width: 768px) {
      .hero-image {
        height: 400px;
      }
    }

    @media (min-width: 1024px) {
      .hero-image {
        height: 500px;
      }
    }

    @media (min-width: 1440px) {
      .hero-image {
        height: 600px;
      }
    }

    /* Overlay Content */
    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 1rem;
    }

    .hero-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: white;
      margin-bottom: 0.5rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .hero-subtitle {
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 1rem;
      max-width: 280px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    @media (min-width: 640px) {
      .hero-title {
        font-size: 2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
        max-width: 400px;
      }
    }

    @media (min-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.125rem;
        max-width: 500px;
      }
    }

    @media (min-width: 1024px) {
      .hero-title {
        font-size: 3rem;
      }

      .hero-subtitle {
        font-size: 1.25rem;
        max-width: 600px;
      }
    }

    /* CTA Button */
    .hero-cta {
      background: linear-gradient(45deg, #10b981, #34d399);
      color: white;
      font-weight: bold;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 2px solid transparent;
    }

    .hero-cta:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
      border-color: white;
    }

    @media (min-width: 640px) {
      .hero-cta {
        padding: 1rem 2rem;
        font-size: 1.125rem;
      }
    }

    /* Loading animations */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Scroll indicators */
    .scroll-indicator {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 12px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .drag-scroll:hover .scroll-indicator {
      opacity: 1;
    }

    /* Existing styles */
    .strikethrough {
      position: relative;
      font-weight: bold;
    }

    .strikethrough:before {
      position: absolute;
      content: "";
      left: 0;
      top: 50%;
      right: 0;
      border-top: 1.5px solid;
      border-color: #000;
      transform: rotate(-25deg);
      color: #000;
    }

    .slider-container {
      overflow: hidden;
      position: relative;
    }

    .slider {
      display: flex;
      backface-visibility: hidden;
      transform: translate3d(0, 0, 0);
    }

    #slider-copa {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
    }

    #slider-copa::-webkit-scrollbar {
      display: none;
    }

    #slider-copa>div {
      scroll-snap-align: start;
      flex-shrink: 0;
    }

    /* Enhanced scrollbar hiding and snap scroll for Odds cards */
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
      -webkit-overflow-scrolling: touch;
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }

    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory {
      scroll-snap-type: x mandatory;
    }

    .snap-start {
      scroll-snap-align: start;
    }

    /* Improved mobile card responsive behavior */
    .odds-card {
      scroll-snap-align: start;
      min-width: 280px;
    }

    @media (max-width: 640px) {
      .odds-card {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
      }
    }

    /* Countdown Timer Styles */
    .countdown-container {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      margin: 2rem 0;
    }

    .countdown-box {
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
      color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      text-align: center;
      min-width: 80px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .countdown-number {
      font-size: 1.5rem;
      font-weight: bold;
      display: block;
    }

    .countdown-label {
      font-size: 0.75rem;
      text-transform: uppercase;
      opacity: 0.8;
    }

    @media (min-width: 640px) {
      .countdown-box {
        min-width: 100px;
        padding: 1.5rem;
      }

      .countdown-number {
        font-size: 2rem;
      }

      .countdown-label {
        font-size: 0.875rem;
      }
    }

    /* Enhanced Animation for Banner */
    .animate-scroll {
      animation: scroll-left 80s linear infinite;
    }

    @keyframes scroll-left {
      0% {
        transform: translateX(0%);
      }

      100% {
        transform: translateX(-50%);
      }
    }

    /* Interactive hover effects */
    .fixture-card {
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .fixture-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .top-scorer-card {
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .top-scorer-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .top-scorer-card:hover::before {
      left: 100%;
    }

    .top-scorer-card:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    /* ODDS TURBINADAS SECTION CUSTOM BG */
    .odds-turbinadas-section {
      background: #000000 !important;
      background-color: #000000 !important;
      background-image: none !important;
    }

    /* RESPONSIVE BANNER IMAGE */
    .banner-image {
      width: 100%;
      height: auto;
      object-fit: cover;
      object-position: center center;
      display: block;
    }

    /* Mobile - Manter seção central visível */
    @media (max-width: 640px) {
      .banner-image {
        height: 250px;
        object-position: center 30%;
      }
    }

    /* Tablet - Ajustar posicionamento */
    @media (min-width: 641px) and (max-width: 1024px) {
      .banner-image {
        height: 350px;
        object-position: center 35%;
      }
    }

    /* Desktop - Mostrar imagem completa */
    @media (min-width: 1025px) {
      .banner-image {
        height: auto;
        object-position: center center;
      }
    }

    /* Telas muito pequenas - Foco na área central */
    @media (max-width: 480px) {
      .banner-image {
        height: 200px;
        object-position: center 25%;
      }
    }

    /* AJUSTE ESPECÍFICO PARA TEXTO EM TELAS < 720px */
    @media (max-width: 720px) {
      #copy-bannet-text {
        bottom: 0.25rem !important;
        /* Equivale a bottom-1 */
      }
    }

    /* Telas muito pequenas - posicionamento com top */
    @media (max-width: 480px) {
      #copy-bannet-text {
        top: 12rem !important;
        bottom: auto !important;
      }
    }
  </style>
  
  <!-- Script principal da aplicação -->
  <script src="main-copa-do-mundo-validacao.js"></script>

</head>

<body class="bg-gray-100 min-h-screen">

  <!-- Banner and Hero section -->
  <div class="w-full relative">
    <img src="https://next-minio-b2b.devcore.at/next-cms/Uqc0cncFJhwc_WktTtfh9w.webp" sizes="100vw" class="banner-image"
      alt="Copa do Mundo de Clubes 2025 - Banner Principal" loading="eager">
    <div id="copy-bannet-text"
      class="absolute bottom-2 sm:bottom-4 md:bottom-5 lg:bottom-6 xl:bottom-1 left-0 right-0 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20">
      <div class="flex flex-col justify-start items-start gap-1 sm:gap-2 w-full max-w-4xl">
        <div
          class="text-amber-300 text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-extrabold font-['Inter'] uppercase leading-tight">
          Mundial de Clubes</div>
        <div
          class="text-white text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium font-['Inter'] leading-relaxed max-w-3xl lg:max-w-none">
          Acompanhe tudo do mundial e aproveite as melhores Odds na Casa!</div>
      </div>
    </div>
  </div>

  <!-- Odds Turbinadas Section -->
  <div class="w-full px-4 sm:px-6 md:px-20 pt-24 pb-14 bg-black">
    <div class="max-w-7xl">
      <div class="flex flex-col gap-8">
        <div class="flex flex-col gap-4">
          <!-- Special Markets -->
          <div class="self-stretch pr-4 inline-flex flex-col justify-start items-start gap-3">
            <h2 class="text-[#E9C043] text-2xl font-semibold">Mercados especiais</h2>
            <p class="text-white text-base"> As melhores odds para você sentir a emoção do mundial a cada lance.</p>

            <div class="flex items-center gap-2">
              <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                    fill="white" />
                </svg>
                <p style="padding-left: 0.5rem;">Deslize para o lado e explore mais mercados. </p>
              </div>

            </div>
            <div class="w-full h-full rounded-lg" style="border-radius: 100px">
              <cms-widget-ref widget-id="4836" widget-identifier="copa-do-mundo-clubes-hb"></cms-widget-ref>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>

  <!-- Standings & Knockout Section -->
  <div class="w-full max-w-6xl mx-auto mt-8 mb-8 px-4">
    <div class="flex flex-col gap-6">
      <!-- Section Header & Phase Navigation -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="text-center mb-4">
          <h2 id="phase-title" class="text-2xl font-bold text-black mb-2 font-sans">Classificação dos Grupos</h2>
          <p id="phase-description" class="text-black text-sm font-normal font-sans">32 times divididos em 8 grupos de 4. Os dois melhores de cada grupo avançam para a próxima fase.</p>
        </div>
        <div class="flex justify-between items-center border-t border-gray-200 pt-4">
          <button id="prev-phase" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition disabled:opacity-50 disabled:cursor-not-allowed">Anterior</button>
          <div id="group-selector-container" class="flex-grow mx-4">
            <p id="phase-instructions" class="text-center text-sm text-gray-600 mb-2">Selecione um grupo para ver a classificação detalhada</p>
            <select id="group-select" class="w-full p-2 border border-gray-300 rounded-md"></select>
          </div>
          <button id="next-phase" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed">Próxima</button>
        </div>
      </div>

      <!-- Dynamic Content Area for Standings/Fixtures -->
      <div id="standings-section" class="min-h-[400px]">
        <!-- Content will be loaded by main-copa-do-mundo.js -->
      </div>
    </div>
  </div>

  <!-- Animated Banner Section -->
  <div class="w-full bg-[#E9C043] py-4 overflow-hidden">
    <div id="slider-motion" class="flex whitespace-nowrap">
      <div
        class="flex items-center justify-start text-[#070300] text-base font-bold font-sans uppercase leading-relaxed whitespace-nowrap animate-scroll">
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <!-- Duplicação do conteúdo para preencher todo o espaço -->
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
      </div>
    </div>
  </div> 
  
  <!-- Topscore rankings-->
  <div class="w-full py-14 bg-gradient-to-b from-stone-900 to-stone-950">
    <div class="max-w-6xl mx-auto px-4 sm:px-8 lg:px-20 flex flex-col justify-start items-start gap-8">
      <div class="w-full flex flex-col justify-start items-center text-center gap-3">
        <div class="text-amber-300 text-3xl font-extrabold font-['Inter']">Artilheiros</div>
        <div class="text-white text-base font-normal font-['Inter'] max-w-3xl">Quem será o goleador do Mundial? Acompanhe a corrida pela artilharia e as maiores promessas do torneio.</div>
        <div class="flex justify-center items-center gap-1">
          <div class="w-5 h-5 relative">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z" fill="white" />
            </svg>
          </div>
          <div class="text-white text-xs font-normal font-['Inter'] leading-none">Confira o ranking completo dos artilheiros!</div>
        </div>
      </div>
      
      <!-- Container para ranking dos artilheiros -->
      <div id="topscore-rankings" class="w-full h-auto min-h-[470px] flex flex-col justify-start items-center">
        <!-- Conteúdo será inserido via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Responsibility Game -->
  <div class="self-stretch py-14 bg-gray-100 flex flex-col gap-8">
    <div class="self-stretch px-4 flex flex-col gap-3">
      <div class="text-slate-900 text-3xl font-extrabold font-sans text-center">Jogue com responsabilidade!</div>
      <div class="self-stretch justify-center text-slate-700 text-base font-normal font-sans text-center">
        A emoção do mundial começa aqui! Acompanhe todos os jogos, confira estatísticas e aproveite odds turbinadas,
        tudo em um só lugar!
      </div>
      <div class="flex justify-center items-center gap-1">
        <div class="self-stretch inline-flex justify-center text-slate-700 text-sm font-normal font-sans"></div>
        <p class="self-stretch justify-center text-slate-700 text-base font-normal font-sans text-center"
          style="padding-left: 0.5rem;">Leia todas as informações disponíveis na nossa seção de <span
            class="text-emerald-700 font-semibold underline">
            <a href="https://casadeapostas.bet.br/br/jogo-responsavel">Jogo Responsável</a></span> e dos nos nossos
          <span class="text-emerald-700 font-semibold underline">
            <a href="https://casadeapostas.bet.br/br/terms-and-conditions"> Termos e Condições</a></span>.</p>
      </div>
    </div>
  </div>
  
  <div class="w-full px-4 flex flex-col items-center">
    <div class="w-full max-w-4xl">
    </div>
  </div>

  <!-- Main script -->
  <script src="main-copa-do-mundo.js"></script>

</body>

</html>
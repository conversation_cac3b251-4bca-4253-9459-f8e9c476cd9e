# 🏆 Globo Esporte Scraper - Mundial de Clubes

Este conjunto de arquivos implementa um sistema completo para extrair dados do Mundial de Clubes diretamente do site do Globo Esporte e integrá-los às landing pages.

## 📁 Arquivos Incluídos

### 1. `globo-esporte-scraper.js`
**Script principal** que realiza o scraping dos dados do Globo Esporte.

**Funcionalidades:**
- ✅ Extração de artilheiros (top scorers)
- ✅ Classificação dos grupos
- ✅ Jogos/fixtures com status
- ✅ Notícias relacionadas
- ✅ Sistema de cache (5 minutos)
- ✅ Fallback com dados mockados
- ✅ Tratamento robusto de erros

### 2. `landing-integration.js`
**Script de integração** que conecta o scraper às landing pages existentes.

**Funcionalidades:**
- ✅ Atualização automática dos dados (5 em 5 minutos)
- ✅ Botão de atualização manual
- ✅ Indicadores visuais de status
- ✅ Integração com elementos existentes das páginas
- ✅ Auto-inicialização inteligente

### 3. `test-scraper.html`
**Página de demonstração** para testar o funcionamento do scraper.

**Funcionalidades:**
- ✅ Interface visual para testes
- ✅ Visualização dos dados extraídos
- ✅ Controle de cache manual
- ✅ Visualização de dados brutos (JSON)
- ✅ Status em tempo real

## 🚀 Como Usar

### Opção 1: Integração Automática nas Landing Pages

1. **Adicione os scripts** no HTML das landing pages:
```html
<!-- Antes do fechamento do </body> -->
<script src="get-ranking-top-scorer/globo-esporte-scraper.js"></script>
<script src="get-ranking-top-scorer/landing-integration.js"></script>
```

2. **Auto-inicialização**: O sistema detecta automaticamente se está em uma página do Mundial de Clubes e se inicializa.

3. **Controle manual** (opcional):
```javascript
// Parar atualização automática
window.mundialIntegration.stopAutoUpdate();

// Atualizar manualmente
window.mundialIntegration.updatePageData();

// Reiniciar atualização automática
window.mundialIntegration.startAutoUpdate();
```

### Opção 2: Uso Manual do Scraper

```javascript
// Instanciar o scraper
const scraper = new GloboEsporteScraper();

// Buscar dados (com cache)
const dados = await scraper.getData();

// Buscar dados frescos (sem cache)
const dadosFrescos = await scraper.getData(false);

// Acessar dados específicos
console.log('Artilheiros:', dados.artilheiros);
console.log('Classificação:', dados.classificacao);
console.log('Jogos:', dados.jogos);
console.log('Notícias:', dados.noticias);
```

### Opção 3: Testando o Sistema

1. Abra o arquivo `test-scraper.html` no navegador
2. Clique em "🚀 Testar Scraper" para executar
3. Visualize os resultados nas seções organizadas
4. Use os controles de cache conforme necessário

## 🔧 Configurações

### Cache
- **Duração**: 5 minutos (300.000ms)
- **Armazenamento**: localStorage do navegador
- **Chaves**: `globo_mundial_data` e `globo_mundial_timestamp`

### Atualização Automática
- **Intervalo**: 5 minutos (300.000ms)
- **Auto-start**: Sim (em páginas do Mundial de Clubes)
- **Controle**: Via `window.mundialIntegration`

### CORS Proxy
- **Serviço**: allorigins.win
- **Motivo**: Evitar bloqueios de CORS do Globo Esporte
- **Fallback**: Dados mockados se falhar

## 📊 Estrutura dos Dados

### Artilheiros
```javascript
{
  position: 1,
  name: "Erling Haaland",
  team: "Manchester City", 
  goals: 8,
  image: "https://..."
}
```

### Classificação
```javascript
{
  group: "Grupo A",
  teams: [
    {
      position: 1,
      name: "Manchester City",
      points: 9,
      played: 3,
      wins: 3,
      draws: 0,
      losses: 0,
      goalsFor: 8,
      goalsAgainst: 2
    }
  ]
}
```

### Jogos
```javascript
{
  homeTeam: "Manchester City",
  awayTeam: "Al Ain FC", 
  date: "2025-06-15",
  time: "16:00",
  score: "3-0",
  status: "Finalizado"
}
```

## 🛡️ Tratamento de Erros

### Cenários Cobertos
- ✅ Falha de rede/conectividade
- ✅ Bloqueio CORS
- ✅ Mudanças na estrutura HTML do site
- ✅ Elementos DOM não encontrados
- ✅ Dados malformados ou ausentes

### Estratégias de Fallback
1. **Cache local** (se disponível e recente)
2. **Dados mockados** com times reais
3. **Logs informativos** para debug
4. **Indicadores visuais** de erro para o usuário

## 🎯 Elementos da Landing Page

### IDs Necessários
- `#artilheiros-copa` - Container dos artilheiros
- `#standings-groups` - Container da classificação
- `#group-select` - Seletor de grupos
- `#fixtures-copa` - Container dos jogos

### Variáveis Globais
- `window.artilheiros` - Array de artilheiros
- `window.standingsGroupsData` - Dados de classificação
- `window.mundialIntegration` - Instância da integração

## 🔍 Debug e Monitoramento

### Console Logs
O sistema emite logs detalhados:
- 🔄 Início de operações
- ✅ Sucesso nas operações
- ❌ Erros e fallbacks
- 💾 Operações de cache
- 📊 Dados extraídos

### Indicadores Visuais
- **Sucesso**: Notificação verde (3s)
- **Erro**: Notificação vermelha (5s)
- **Botão manual**: Canto inferior direito
- **Loading**: Animação no botão

## 📱 Responsividade

O sistema funciona em:
- ✅ Desktop
- ✅ Tablet
- ✅ Mobile
- ✅ PWA/WebView

## ⚡ Performance

### Otimizações
- Cache local (reduz requisições)
- Lazy loading de dados
- Debounce em atualizações manuais
- Cleanup automático de recursos

### Métricas Típicas
- **Primeira carga**: 2-5 segundos
- **Cache hit**: < 100ms
- **Atualização**: 1-3 segundos
- **Fallback**: < 500ms

## 🔐 Segurança

### Considerações
- ✅ Sanitização de dados HTML
- ✅ Validação de URLs de imagem
- ✅ Escape de caracteres especiais
- ✅ Limite de dados armazenados
- ✅ Timeout em requisições

## 📄 Licença

Este código é destinado ao uso interno da Casa de Apostas para as landing pages do Mundial de Clubes 2025.

---

**Desenvolvido para Casa de Apostas - Mundial de Clubes 2025** 🏆

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIFA Club World Cup Fixtures and Standings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f9;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-top: 1.5rem;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007bff;
            color: white;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        @media (max-width: 600px) {
            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>
    <h1>FIFA Club World Cup Fixtures</h1>
    <table id="fixturesTable" border="1">
        <thead>
            <tr>
                <th>Match</th>
                <th>Home Team</th>
                <th>Away Team</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

    <h1>FIFA Club World Cup Standings</h1>
    <div id="standings-groups" class="w-full mt-8 flex flex-col gap-8"></div>

    <script>
        // Fetch and display fixtures
        const FIXTURES_API_URL = 'http://127.0.0.1:5000/fixtures';

        async function getFixtures() {
            const response = await fetch(FIXTURES_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ group: 'Required' }) // ou substitua por outro grupo válido
            });
            if (response.ok) {
                const data = await response.json();
                const fixtures = data.fixtures || data.response || data;

                const tableBody = document.querySelector('#fixturesTable tbody');
                tableBody.innerHTML = '';
                (fixtures || []).forEach(fixture => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${fixture.match_id || fixture.id || ''}</td>
                        <td>${fixture.home_team || (fixture.teams && fixture.teams.home) || ''}</td>
                        <td>${fixture.away_team || (fixture.teams && fixture.teams.away) || ''}</td>
                        <td>${fixture.date || fixture.match_date || ''}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                alert('Erro ao buscar os dados!');
            }
        }

        // Fetch and display standings
        const STANDINGS_API_URL = 'https://v3.football.api-sports.io/standings?league=15&season=2025';
        const API_KEY = '6ab9fabfb32d18cad9adb9525d1076ac'; // Substitua pela sua chave da API-SPORTS

        async function getStandings() {
            const response = await fetch(STANDINGS_API_URL, {
                method: 'GET',
                headers: {
                    'x-apisports-key': API_KEY
                }
            });
            if (response.ok) {
                const data = await response.json();
                renderStandingsGroups(data);
            } else {
                document.getElementById('standings-groups').innerHTML = '<p>Erro ao buscar os dados da classificação!</p>';
            }
        }

        function renderStandingsGroups(apiData) {
            const container = document.getElementById('standings-groups');
            container.innerHTML = '';
            if (!apiData.response || !apiData.response[0] || !apiData.response[0].league.standings) return;
            const groups = apiData.response[0].league.standings;
            groups.forEach((groupArr, idx) => {
                if (!groupArr.length) return;
                const groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
                let table = `<div style="overflow-x:auto;margin-bottom:2rem;">
                    <h3 style="font-size:1.2rem;font-weight:bold;color:#006E4E;margin-bottom:0.5rem;">${groupName}</h3>
                    <table style="min-width:600px;width:100%;background:#fff;border-radius:8px;box-shadow:0 2px 8px #0001;">
                        <thead style="background:#f3f4f6;">
                            <tr>
                                <th>Pos</th>
                                <th>Time</th>
                                <th>Pts</th>
                                <th>J</th>
                                <th>V</th>
                                <th>E</th>
                                <th>D</th>
                                <th>GP</th>
                                <th>GC</th>
                                <th>SG</th>
                            </tr>
                        </thead>
                        <tbody>`;
                groupArr.forEach(team => {
                    table += `<tr style="text-align:center;">
                        <td>${team.rank}</td>
                        <td style="text-align:left;display:flex;align-items:center;gap:8px;"><img src="${team.team.logo}" alt="${team.team.name}" style="width:24px;height:24px;border-radius:50%;border:1px solid #eee;"/>${team.team.name}</td>
                        <td><b style='color:#006E4E;'>${team.points}</b></td>
                        <td>${team.all.played}</td>
                        <td>${team.all.win}</td>
                        <td>${team.all.draw}</td>
                        <td>${team.all.lose}</td>
                        <td>${team.all.goals.for}</td>
                        <td>${team.all.goals.against}</td>
                        <td>${team.goalsDiff}</td>
                    </tr>`;
                });
                table += '</tbody></table></div>';
                container.insertAdjacentHTML('beforeend', table);
            });
        }

        getFixtures();
        getStandings();
    </script>
</body>
</html>

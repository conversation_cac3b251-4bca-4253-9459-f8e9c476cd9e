<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Teste de Chaveamento</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }
  </style>
</head>
<body class="bg-gray-100 p-4">
  <div class="max-w-6xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">Teste de Chaveamento</h1>
    
    <!-- Bracket Layout Dinâmico -->
    <div id="bracket-dinamico" class="grid grid-cols-1 lg:grid-cols-2 gap-8"></div>
  </div>

  <!-- Carrega os dados do chaveamento antes do script principal -->
  <script src="chaveamento.js"></script>
  
  <!-- Script para teste -->
  <script>
    // Mapeamento de bandeiras por clube
    const TEAM_FLAGS = {
      'Palmeiras': 'br',
      'Botafogo': 'br',
      'Benfica': 'pt',
      'Chelsea': 'gb-eng',
      'Internazionale': 'it',
      'Fluminense': 'br',
      'Manchester City': 'gb-eng',
      'Al-Hilal': 'sa',
      'Paris Saint-Germain': 'fr',
      'Inter Miami': 'us',
      'Flamengo': 'br',
      'Bayern de Munique': 'de',
      'Borussia Dortmund': 'de',
      'Monterrey': 'mx',
      'Real Madrid': 'es',
      'Juventus': 'it'
    };

    // Função para obter a bandeira do país do time
    function getTeamFlag(teamName) {
      const code = TEAM_FLAGS[teamName];
      return code ? `https://flagcdn.com/20x15/${code}.png` : 'https://via.placeholder.com/20x15/cccccc/666666?text=?';
    }

    // Renderiza o chaveamento (bracket) do torneio dinamicamente
    function renderBracketOitavas() {
      console.log('🔄 Iniciando renderização do chaveamento...');
      
      // Verifica se o objeto classificacao está definido
      if (typeof classificacao === 'undefined' || !classificacao.secao) {
        console.warn('⚠️ Dados do chaveamento não encontrados. Verifique se o arquivo chaveamento.js foi carregado.');
        return;
      }
      
      // Extrai as chaves do objeto classificacao
      const chaves = classificacao.secao.flatMap(sec => sec.chave);
      
      // Divide as partidas em dois grupos para visualização: Chave A e Chave B
      let htmlA = '';
      let htmlB = '';
      
      chaves.forEach((chave, idx) => {
        const jogo = chave.jogos[0];
        
        // Verifica se o jogo e suas equipes existem
        if (!jogo || !jogo.equipes) return;
        
        const mandante = jogo.equipes.mandante;
        const visitante = jogo.equipes.visitante;
        const data = jogo.data_realizacao ? new Date(jogo.data_realizacao).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }) : 'Data a definir';
        const hora = jogo.hora_realizacao || '--:--';
        const local = jogo.sede?.nome_popular || 'Local a definir';
        
        // Obtém as bandeiras dos países dos times
        const mandanteFlag = getTeamFlag(mandante.nome_popular);
        const visitanteFlag = getTeamFlag(visitante.nome_popular);
        
        // Template do card de jogo
        const card = `
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div class="text-xs text-gray-500 mb-2 flex justify-between">
              <span>${data} • ${hora}</span>
              <span>${local}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3 flex-1">
                <img src="${mandante.escudo}" alt="${mandante.nome_popular}" class="w-12 h-12 object-contain rounded-sm bg-white border border-gray-100" 
                    onerror="this.src='https://via.placeholder.com/48x48/cccccc/666666?text=?'">
                <div class="flex flex-col">
                  <div class="text-black font-semibold text-base font-sans">${mandante.nome_popular}</div>
                  <img src="${mandanteFlag}" alt="Bandeira ${mandante.nome_popular}" class="w-5 h-4 object-cover rounded-sm">
                </div>
              </div>
              <div class="px-4">
                <span class="text-amber-500 font-bold text-xl">VS</span>
              </div>
              <div class="flex items-center gap-3 flex-1 justify-end">
                <div class="flex flex-col items-end">
                  <div class="text-black font-semibold text-base font-sans">${visitante.nome_popular}</div>
                  <img src="${visitanteFlag}" alt="Bandeira ${visitante.nome_popular}" class="w-5 h-4 object-cover rounded-sm">
                </div>
                <img src="${visitante.escudo}" alt="${visitante.nome_popular}" class="w-12 h-12 object-contain rounded-sm bg-white border border-gray-100" 
                    onerror="this.src='https://via.placeholder.com/48x48/cccccc/666666?text=?'">
              </div>
            </div>
          </div>
        `;
        
        // Adiciona o card à chave A ou B com base no índice
        if (idx < 4) {
          htmlA += card;
        } else {
          htmlB += card;
        }
      });
      
      // Renderiza o HTML final com as duas chaves
      document.getElementById('bracket-dinamico').innerHTML = `
        <div class="space-y-4">
          <h3 class="text-lg font-bold text-black text-center font-sans mb-4 bg-amber-400 text-black py-2 rounded-lg">CHAVE A</h3>
          ${htmlA}
        </div>
        <div class="space-y-4">
          <h3 class="text-lg font-bold text-black text-center font-sans mb-4 bg-amber-400 text-black py-2 rounded-lg">CHAVE B</h3>
          ${htmlB}
        </div>
      `;
      
      console.log('✅ Chaveamento renderizado com sucesso!');
    }

    // Executar quando o DOM estiver pronto
    document.addEventListener('DOMContentLoaded', renderBracketOitavas);
  </script>
</body>
</html>

// --- JS para SPA/CMS - Copa do Mundo de Clubes ---
// Atualizado em 18:34 às 20/06/25


// Função para aguardar elemento aparecer no DOM
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver((mutations, obs) => {
      const element = document.querySelector(selector);
      if (element) {
        obs.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Elemento ${selector} não encontrado em ${timeout}ms`));
    }, timeout);
  });
}

// Função para toggle dos termos
function toggleTerms() {
  const content = document.getElementById('terms-content');
  const icon = document.getElementById('terms-icon');
  const chevron = document.getElementById('chevron-icon');
  if (content && icon && chevron) {
    if (content.classList.contains('hidden')) {
      content.classList.remove('hidden');
      icon.textContent = '−';
      chevron.style.transform = 'rotate(180deg)';
    } else {
      content.classList.add('hidden');
      icon.textContent = '+';
      chevron.style.transform = 'rotate(0deg)';
    }
  }
}

// API dos artilheiros
const ARTILHEIROS_API_URL = 'https://cdn.jsdelivr.net/gh/mu-costa/artilheiros@refs/heads/main/artilheiros.json';

// Função para buscar artilheiros da API
async function fetchArtilheiros() {
  try {
    console.log('🔄 Buscando dados dos artilheiros...');
    const response = await fetch(ARTILHEIROS_API_URL);
    
    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status}`);
    }
    
    const artilheiros = await response.json();
    console.log('✅ Dados dos artilheiros carregados:', artilheiros.length, 'jogadores');
    return artilheiros;
  } catch (error) {
    console.warn('⚠️ Erro ao buscar artilheiros:', error.message);
    
    // Fallback com dados mockados
    return [
      {
        "jogador-foto": "https://s.sde.globo.com/media/person_role/2024/06/14/photo_140x140_jGREsGd.png",
        "jogador-escudo": "https://s.sde.globo.com/media/organizations/2017/09/22/Bayer-Munique-65.png",
        "jogador-nome": "Musiala",
        "jogador-posicao": "Meio-campo",
        "jogador-gols": "3"
      },
      {
        "jogador-foto": "https://s.sde.globo.com/media/person_role/2019/03/13/e3906271f3caccb8796dc63477b6a451_140x140.png",
        "jogador-escudo": "https://s.sde.globo.com/media/organizations/2017/09/22/Bayer-Munique-65.png",
        "jogador-nome": "Coman",
        "jogador-posicao": "Atacante",
        "jogador-gols": "2"
      },
      {
        "jogador-foto": "https://s.sde.globo.com/media/person_role/2022/11/01/photo_140x140_yB5pM4u.png",
        "jogador-escudo": "https://s.sde.globo.com/media/organizations/2025/06/09/Juventus-65x65.png",
        "jogador-nome": "Kolo Muani",
        "jogador-posicao": "Atacante",
        "jogador-gols": "2"
      },
      {
        "jogador-foto": "https://s.sde.globo.com/media/person_role/2019/04/16/2144ecb394516ea16dcf9b465a1bdefe_140x140.png",
        "jogador-escudo": "https://s.sde.globo.com/media/organizations/2023/07/25/inter-miami-65x65-62396.png",
        "jogador-nome": "Messi",
        "jogador-posicao": "Atacante",
        "jogador-gols": "1"
      },
      {
        "jogador-foto": "https://s.sde.globo.com/media/person_role/2020/06/13/7f7d74c23caddf25e45fc48416ddc6d7_140x140.png",
        "jogador-escudo": "https://s.sde.globo.com/media/organizations/2021/03/31/65_Inter_de_Milão_2021.png",
        "jogador-nome": "Lautaro Martínez",
        "jogador-posicao": "Atacante",
        "jogador-gols": "1"
      }
    ];
  }
}

// Renderização dinâmica dos artilheiros (top scorers) - Aguarda elemento e busca da API
async function renderArtilheiros() {
  try {
    const artilheirosDiv = await waitForElement('#artilheiros-copa', 5000);
    
    // Busca dados da API
    const artilheiros = await fetchArtilheiros();
    
    // Ordena por gols (decrescente) e pega os top 10
    const topArtilheiros = artilheiros
      .sort((a, b) => parseInt(b['jogador-gols']) - parseInt(a['jogador-gols']))
      .slice(0, 10);
    
    artilheirosDiv.innerHTML = '';
    
    topArtilheiros.forEach((artilheiro, idx) => {
      const gols = parseInt(artilheiro['jogador-gols']) || 0;
      const posicao = artilheiro['jogador-posicao'] || 'N/A';
      
      artilheirosDiv.insertAdjacentHTML('beforeend', `        <div class="flex-shrink-0 w-36 sm:w-40 md:w-48 lg:w-56 rounded-lg bg-gradient-to-b from-neutral-900 to-neutral-800 shadow-lg overflow-hidden flex flex-col items-center justify-between border border-amber-500/20">
          <div class="flex flex-col items-center justify-center bg-gradient-to-r from-amber-600 to-amber-500 p-3 sm:p-4 w-full">
            <div class="text-white text-xl sm:text-2xl font-bold mb-1 sm:mb-2">#${idx + 1}</div>
            <img src="${artilheiro['jogador-foto']}" alt="${artilheiro['jogador-nome']}" class="w-14 h-14 sm:w-16 sm:h-16 rounded-full border-2 border-white mb-1 sm:mb-2 object-cover shadow-md bg-white" onerror="this.src='https://via.placeholder.com/64x64/cccccc/666666?text=?'">
            <div class="flex items-center gap-1 mb-1 sm:mb-2">
              <img src="${artilheiro['jogador-escudo']}" alt="Escudo" class="w-6 h-6 sm:w-8 sm:h-8 rounded-full border border-white object-cover" onerror="this.src='https://via.placeholder.com/32x32/cccccc/666666?text=?'">
              <span class="block text-xs font-semibold text-white/80 truncate max-w-[80px] sm:max-w-[100px]">${posicao}</span>
            </div>
            <span class="block text-xl sm:text-2xl font-bold text-white">${gols} <span class='text-sm sm:text-base font-normal'>gol${gols !== 1 ? 's' : ''}</span></span>
          </div>
          <div class="p-2 sm:p-3 w-full text-center">
            <h4 class="text-sm sm:text-base font-semibold text-amber-300 break-words leading-tight">${artilheiro['jogador-nome']}</h4>
          </div>
        </div>
      `);
    });
    
    console.log('✅ Artilheiros renderizados com sucesso:', topArtilheiros.length, 'jogadores');
  } catch (error) {
    console.warn('⚠️ Container de artilheiros não encontrado:', error.message);
  }
}

// Função para garantir que os artilheiros sejam renderizados também na variável global window.artilheiros (compatibilidade)
async function setupArtilheirosGlobal() {
  try {
    const artilheiros = await fetchArtilheiros();
    
    // Converte formato da API para formato usado anteriormente
    window.artilheiros = artilheiros
      .sort((a, b) => parseInt(b['jogador-gols']) - parseInt(a['jogador-gols']))
      .slice(0, 10)
      .map(artilheiro => ({
        name: artilheiro['jogador-nome'],
        logo: artilheiro['jogador-foto'],
        team: artilheiro['jogador-posicao'],
        goals: parseInt(artilheiro['jogador-gols']) || 0
      }));
    
    console.log('✅ window.artilheiros configurado com', window.artilheiros.length, 'jogadores');
  } catch (error) {
    console.warn('⚠️ Erro ao configurar artilheiros globais:', error.message);
    window.artilheiros = [];
  }
}

// Standings API - Adaptado para SPA
const STANDINGS_API_URL = 'https://v3.football.api-sports.io/standings?league=15&season=2025';
const FIXTURES_API_URL = 'https://v3.football.api-sports.io/fixtures';
const API_KEY = '6ab9fabfb32d18cad9adb9525d1076ac';
let standingsGroupsData = [];
let fixturesData = {}; // Cache for fixtures

async function getFixtures(roundName) {
  if (fixturesData[roundName]) {
    console.log(`Returning cached fixtures for ${roundName}`);
    return fixturesData[roundName];
  }

  const url = `${FIXTURES_API_URL}?league=15&season=2025&round=${encodeURIComponent(roundName)}`;
  
  try {
    updateStandingsUI(true, false, `Carregando confrontos para ${roundName}...`);
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'x-apisports-key': API_KEY }
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (data.response && data.response.length > 0) {
      console.log(`✅ Fixtures recebidos para ${roundName}:`, data.response.length, 'jogos');
      fixturesData[roundName] = data.response; // Cache the data
      return data.response;
    } else {
      console.warn(`⚠️ Nenhum jogo encontrado para ${roundName}.`);
      fixturesData[roundName] = []; // Cache empty result
      return [];
    }
  } catch (error) {
    console.error(`❌ Erro ao buscar jogos para ${roundName}:`, error);
    updateStandingsUI(false, true, `Não foi possível carregar os jogos para ${roundName}.`);
    fixturesData[roundName] = []; // Cache empty result on error
    return [];
  }
}

async function getStandings() {
  try {
    console.log('🔄 Iniciando busca de standings...');
    
    // Show loading state
    updateStandingsUI(true);
      // Aguarda elementos estarem disponíveis
    await waitForElement('#group-select', 5000);
    await waitForElement('#standings-section', 5000);
    
    const response = await fetch(STANDINGS_API_URL, {
      method: 'GET',
      headers: {
        'x-apisports-key': API_KEY
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.response && data.response[0] && data.response[0].league.standings) {
        standingsGroupsData = data.response[0].league.standings;
        renderGroupSelect(standingsGroupsData);
        renderEnhancedStandingsGroup(0); // Use enhanced function
        console.log('✅ Standings carregados com sucesso');
      }
    } else {
      throw new Error(`Erro na API: ${response.status}`);
    }
  } catch (error) {
    console.warn('⚠️ Erro ao carregar standings:', error.message);
    
    // Show error state briefly, then load fallback data
    updateStandingsUI(false, true, 'Carregando dados de demonstração...');
    
    setTimeout(() => {
      // Fallback com dados mockados
      standingsGroupsData = [
        [
          { team: { name: 'Manchester City', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png' }, points: 9, rank: 1, all: { played: 3, win: 3, draw: 0, lose: 0, goals: { for: 8, against: 2 } }, goalsDiff: 6 },
          { team: { name: 'Juventus', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Juventus-Logo.png' }, points: 6, rank: 2, all: { played: 3, win: 2, draw: 0, lose: 1, goals: { for: 5, against: 3 } }, goalsDiff: 2 },
          { team: { name: 'Wydad AC', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8b/Wydad_AC_logo.svg/1200px-Wydad_AC_logo.svg.png' }, points: 3, rank: 3, all: { played: 3, win: 1, draw: 0, lose: 2, goals: { for: 3, against: 5 } }, goalsDiff: -2 },
          { team: { name: 'Al Ain FC', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/1/1b/Al_Ain_FC_logo.svg/1200px-Al_Ain_FC_logo.svg.png' }, points: 0, rank: 4, all: { played: 3, win: 0, draw: 0, lose: 3, goals: { for: 1, against: 7 } }, goalsDiff: -6 }
        ],
        [
          { team: { name: 'Real Madrid', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png' }, points: 9, rank: 1, all: { played: 3, win: 3, draw: 0, lose: 0, goals: { for: 7, against: 1 } }, goalsDiff: 6 },
          { team: { name: 'Borussia Dortmund', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Borussia-Dortmund-Logo.png' }, points: 6, rank: 2, all: { played: 3, win: 2, draw: 0, lose: 1, goals: { for: 4, against: 3 } }, goalsDiff: 1 },
          { team: { name: 'Pachuca', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/b/b5/Pachuca_logo.svg/1200px-Pachuca_logo.svg.png' }, points: 3, rank: 3, all: { played: 3, win: 1, draw: 0, lose: 2, goals: { for: 2, against: 4 } }, goalsDiff: -2 },
          { team: { name: 'Al Hilal', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/1/1b/Al_Hilal_FC_logo.svg/1200px-Al_Hilal_FC_logo.svg.png' }, points: 0, rank: 4, all: { played: 3, win: 0, draw: 0, lose: 3, goals: { for: 1, against: 6 } }, goalsDiff: -5 }
        ]
      ];
      
      try {
        renderGroupSelect(standingsGroupsData);
        renderEnhancedStandingsGroup(0); // Use enhanced function        const container = document.getElementById('standings-section');
        if (container && container.firstElementChild) {
          container.firstElementChild.insertAdjacentHTML('afterbegin', 
            '<div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 rounded-lg"><div class="flex"><div class="ml-3"><p class="text-sm text-yellow-700">⚠️ Exibindo dados de demonstração (API indisponível)</p></div></div></div>'
          );
        }
      } catch (fallbackError) {
        console.error('❌ Erro no fallback:', fallbackError);
        updateStandingsUI(false, true, 'Não foi possível carregar os dados');
      }
    }, 1500);
  }
}

function renderGroupSelect(groups) {
  const select = document.getElementById('group-select');
  if (!select) {
    console.error('Element with id "group-select" not found');
    return;
  }
  
  select.innerHTML = '';
  if (!groups || !Array.isArray(groups)) {
    select.innerHTML = '<option value="-1">Nenhum grupo disponível</option>';
    return;
  }
    groups.forEach((groupArr, idx) => {
    if (!groupArr || !groupArr.length) return;
    let groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
    
    // Convert "Group A" to "Grupo A" if needed
    if (groupName.startsWith('Group ')) {
      groupName = groupName.replace('Group ', 'Grupo ');
    }
    
    const option = document.createElement('option');
    option.value = idx;
    option.textContent = groupName;
    select.appendChild(option);
  });
  
  select.onchange = function () {
    const selectedIndex = Number(this.value);
    if (selectedIndex >= 0) {
      renderEnhancedStandingsGroup(selectedIndex); // Use enhanced function
    }
  };
}

function renderStandingsGroup(idx) {
  const container = document.getElementById('standings-groups');
  if (!container) {
    console.error('Element with id "standings-groups" not found');
    return;
  }
  
  container.innerHTML = '';
  
  if (!standingsGroupsData || !Array.isArray(standingsGroupsData) || 
      !standingsGroupsData[idx] || !standingsGroupsData[idx].length) {
    container.innerHTML = '<p class="text-gray-500 p-4">Nenhum dado disponível para este grupo.</p>';
    return;
  }
    const groupArr = standingsGroupsData[idx];
  let groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
  
  // Convert "Group A" to "Grupo A" if needed
  if (groupName.startsWith('Group ')) {
    groupName = groupName.replace('Group ', 'Grupo ');
  }
  
  let table = `<div class='overflow-x-auto rounded-lg border border-gray-200 bg-white shadow mt-4'>
    <h3 class='text-lg font-bold text-green-700 mb-2 px-4 pt-4'>${groupName}</h3>
    <table class='min-w-[600px] w-full bg-white rounded-lg'>
      <thead class='bg-gradient-to-r from-gray-50 to-gray-100'>
        <tr>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pos</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider text-left'>Time</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pts</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>J</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>V</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>E</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>D</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GP</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GC</th>
          <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>SG</th>
        </tr>
      </thead>
      <tbody class='bg-white divide-y divide-gray-200'>`;
  
  groupArr.forEach(team => {
    table += `<tr class='hover:bg-green-50 transition'>
      <td class='px-2 py-1 text-center font-semibold'>${team.rank}</td>
      <td class='px-2 py-1 flex items-center gap-2'><img src='${team.team.logo}' alt='${team.team.name}' class='w-6 h-6 inline-block rounded-full border' onerror="this.src='https://via.placeholder.com/24x24/cccccc/666666?text=?'" />${team.team.name}</td>
      <td class='px-2 py-1 text-center font-bold text-green-700'>${team.points}</td>
      <td class='px-2 py-1 text-center'>${team.all.played}</td>
      <td class='px-2 py-1 text-center'>${team.all.win}</td>
      <td class='px-2 py-1 text-center'>${team.all.draw}</td>
      <td class='px-2 py-1 text-center'>${team.all.lose}</td>
      <td class='px-2 py-1 text-center'>${team.all.goals.for}</td>
      <td class='px-2 py-1 text-center'>${team.all.goals.against}</td>
      <td class='px-2 py-1 text-center'>${team.goalsDiff}</td>
    </tr>`;
  });
  table += '</tbody></table></div>';
  container.insertAdjacentHTML('beforeend', table);
}

// Função para renderizar ranking completo dos artilheiros em tabela
async function renderRankingArtilheiros() {
  try {
    const rankingDiv = await waitForElement('#ranking-artilheiros', 5000);
    
    // Busca dados da API
    const artilheiros = await fetchArtilheiros();
    
    // Ordena por gols (decrescente)
    const rankingCompleto = artilheiros
      .sort((a, b) => parseInt(b['jogador-gols']) - parseInt(a['jogador-gols']));
    
    rankingDiv.innerHTML = `
      <div class='overflow-x-auto rounded-lg border border-gray-200 bg-white shadow mt-4'>
        <h3 class='text-lg font-bold text-amber-600 mb-2 px-4 pt-4'>🏆 Ranking de Artilheiros - Copa do Mundo de Clubes</h3>
        <table class='min-w-[600px] w-full bg-white rounded-lg'>
          <thead class='bg-gradient-to-r from-amber-50 to-amber-100'>
            <tr>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pos</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider text-left'>Jogador</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider text-left'>Posição</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Gols</th>
            </tr>
          </thead>
          <tbody class='bg-white divide-y divide-gray-200'>
            ${rankingCompleto.map((artilheiro, idx) => {
              const gols = parseInt(artilheiro['jogador-gols']) || 0;
              const posicao = artilheiro['jogador-posicao'] || 'N/A';
              
              return `                <tr class='hover:bg-amber-50 transition ${idx < 3 ? 'bg-amber-25' : ''}'>
                  <td class='px-2 sm:px-3 py-1 sm:py-2 text-center font-bold ${idx === 0 ? 'text-amber-600' : idx < 3 ? 'text-amber-500' : 'text-gray-700'}'>${idx + 1}</td><td class='px-2 sm:px-3 py-1 sm:py-2 flex items-center gap-2 sm:gap-3'>
                    <img src='${artilheiro['jogador-foto']}' alt='${artilheiro['jogador-nome']}' class='w-6 h-6 sm:w-8 sm:h-8 rounded-full border object-cover bg-white' onerror="this.src='https://via.placeholder.com/32x32/cccccc/666666?text=?'" />
                    <div class='flex flex-col'>
                      <span class='font-semibold text-gray-900 text-sm sm:text-base'>${artilheiro['jogador-nome']}</span>
                      <div class='flex items-center gap-1'>
                        <img src='${artilheiro['jogador-escudo']}' alt='Escudo' class='w-3 h-3 sm:w-4 sm:h-4 rounded object-cover' onerror="this.src='https://via.placeholder.com/16x16/cccccc/666666?text=?'" />
                        <span class='text-xs text-gray-500'>Clube</span>
                      </div>
                    </div>
                  </td>                  <td class='px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm text-gray-600'>${posicao}</td>
                  <td class='px-2 sm:px-3 py-1 sm:py-2 text-center'>
                    <span class='inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium ${idx === 0 ? 'bg-amber-100 text-amber-800' : idx < 3 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}'>
                      ${gols} gol${gols !== 1 ? 's' : ''}
                    </span>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;
    
    console.log('✅ Ranking de artilheiros renderizado:', rankingCompleto.length, 'jogadores');
  } catch (error) {
    console.warn('⚠️ Container de ranking de artilheiros não encontrado:', error.message);
  }
}

// Função para renderizar o ranking completo na seção Topscore rankings
async function renderTopscoreRankings() {
  try {
    const rankingContainer = await waitForElement('#topscore-rankings', 5000);
    
    // Busca dados da API
    const artilheiros = await fetchArtilheiros();
    
    // Ordena por gols (decrescente) e pega os top 15
    const topArtilheiros = artilheiros
      .sort((a, b) => parseInt(b['jogador-gols']) - parseInt(a['jogador-gols']))
      .slice(0, 15);      // Cria o cabeçalho
    const headerHTML = `
      <div class="w-full max-w-4xl flex flex-col justify-start items-start gap-1 sm:gap-2">
        <div class="w-full h-8 sm:h-10 py-2 sm:py-2.5 border-t border-b border-white inline-flex justify-start items-center gap-2">
          <div class="flex-1 justify-center text-white text-sm sm:text-base font-normal font-['Inter'] uppercase leading-tight">ranking</div>
          <div class="text-right justify-center text-white text-sm sm:text-base font-normal font-['Inter'] uppercase leading-tight">gols</div>
        </div>
      </div>
    `;
      // Cria o conteúdo da lista
    const playersHTML = topArtilheiros.map((artilheiro, idx) => {
      const gols = parseInt(artilheiro['jogador-gols']) || 0;
      const posicao = artilheiro['jogador-posicao'] || 'N/A';
      const nomeCompleto = artilheiro['jogador-nome'] || 'Nome não disponível';
      const isTop3 = idx < 3;
        return `        <div class="w-full py-2 sm:py-3.5 border-b border-white inline-flex justify-start items-center gap-2 sm:gap-4 hover:bg-white/5 transition-colors duration-200">
          <div class="w-6 sm:w-8 h-7 justify-center ${isTop3 ? 'text-amber-400' : 'text-white/50'} text-2xl sm:text-3xl font-normal font-['Open_Sans']">${idx + 1}</div>
          <div class="flex justify-start items-center gap-1 sm:gap-2 ml-1 sm:ml-2">
            <img class="w-10 h-10 sm:w-12 sm:h-12 relative rounded-[80px] ${isTop3 ? 'border-2 border-amber-400' : 'border border-white/20'} object-cover bg-white" 
                 src="${artilheiro['jogador-foto']}" 
                 alt="${nomeCompleto}"
                 onerror="this.src='https://via.placeholder.com/48x48/374151/9CA3AF?text=${encodeURIComponent(nomeCompleto.charAt(0))}'" />
            <img class="w-5 h-5 sm:w-6 sm:h-6 relative object-cover rounded" 
                 src="${artilheiro['jogador-escudo']}" 
                 alt="Escudo do time"
                 onerror="this.src='https://via.placeholder.com/24x24/374151/9CA3AF?text=?'" />
          </div>
          <div class="flex-1 min-w-0 inline-flex flex-col justify-start items-start">
            <div class="w-full justify-start text-white text-lg sm:text-xl font-normal font-['Inter'] overflow-hidden whitespace-nowrap text-ellipsis">${nomeCompleto}</div>
            <div class="w-full justify-start text-white/50 text-[9px] sm:text-[10px] font-bold font-['Inter'] uppercase overflow-hidden whitespace-nowrap text-ellipsis">${posicao}</div>
          </div>
          <div class="w-12 sm:w-16 text-right justify-center ${isTop3 ? 'text-amber-400' : 'text-white'} text-xl sm:text-2xl font-bold font-['Inter'] leading-relaxed">${gols}</div>
        </div>
      `;
    }).join('');    // Monta o HTML completo
    const containerHTML = `
      ${headerHTML}
      <div class="w-full max-w-4xl flex-1 border-b border-white flex flex-col justify-start items-start overflow-y-auto max-h-[300px] sm:max-h-[400px] scrollbar-thin">
        ${playersHTML}
      </div>
    `;
    
    rankingContainer.innerHTML = containerHTML;
    
    console.log('✅ Topscore rankings renderizado:', topArtilheiros.length, 'jogadores');
  } catch (error) {
    console.warn('⚠️ Container de topscore rankings não encontrado:', error.message);
    
    // Se não encontrar o container, tenta criar uma versão simplificada
    try {
      const fallbackContainer = document.createElement('div');
      fallbackContainer.id = 'topscore-rankings-fallback';
      fallbackContainer.className = 'p-4 text-gray-700 bg-red-100 border border-red-300 rounded mx-4 my-2';
      fallbackContainer.innerHTML = '<p>⚠️ Seção de artilheiros não encontrada no HTML. Verifique se o elemento #topscore-rankings existe.</p>';
      
      // Tenta inserir próximo ao elemento artilheiros-copa se existir
      const artilheirosContainer = document.getElementById('artilheiros-copa');
      if (artilheirosContainer && artilheirosContainer.parentElement) {
        artilheirosContainer.parentElement.appendChild(fallbackContainer);
      } else {
        document.body.appendChild(fallbackContainer);
      }
    } catch (fallbackError) {
      console.error('❌ Erro no fallback do topscore:', fallbackError);
    }
  }
}

// Tournament phases configuration
const TOURNAMENT_PHASES = [
  {
    id: 'groups',
    title: 'Classificação dos Grupos',
    description: '32 times divididos em 8 grupos de 4. Os dois melhores de cada grupo avançam para a próxima fase.',
    instructions: 'Selecione um grupo para ver a classificação detalhada',
    showGroupSelector: true
  },
  {
    id: 'round16',
    title: 'Oitavas de Final',
    description: '16 times classificados disputam em jogos eliminatórios. Quem vencer avança para as quartas.',
    instructions: 'Confira os confrontos das oitavas de final',
    showGroupSelector: false
  },
  {
    id: 'quarter',
    title: 'Quartas de Final',
    description: '8 times restantes lutam por uma vaga na semifinal. A tensão aumenta a cada partida.',
    instructions: 'Veja os duelos das quartas de final',
    showGroupSelector: false
  },
  {
    id: 'semi',
    title: 'Semifinais',
    description: '4 times disputam as duas vagas na grande final da Copa do Mundo de Clubes.',
    instructions: 'Acompanhe as semifinais',
    showGroupSelector: false
  },
  {
    id: 'final',
    title: 'Final',
    description: 'A grande decisão! Dois times lutam pelo título de campeão mundial de clubes.',
    instructions: 'A final da Copa do Mundo de Clubes',
    showGroupSelector: false
  }
];

let currentPhaseIndex = 0;

// Phase navigation functions
function initializePhaseNavigation() {
  const prevButton = document.getElementById('prev-phase');
  const nextButton = document.getElementById('next-phase');
  
  if (prevButton && nextButton) {
    prevButton.addEventListener('click', () => navigatePhase(-1));
    nextButton.addEventListener('click', () => navigatePhase(1));
    
    updatePhaseDisplay();
  }
}

function navigatePhase(direction) {
  const newIndex = currentPhaseIndex + direction;
  
  if (newIndex >= 0 && newIndex < TOURNAMENT_PHASES.length) {
    currentPhaseIndex = newIndex;
    updatePhaseDisplay();
    updatePhaseContent();
  }
}

function updatePhaseDisplay() {
  const phase = TOURNAMENT_PHASES[currentPhaseIndex];
  const prevButton = document.getElementById('prev-phase');
  const nextButton = document.getElementById('next-phase');
  const titleElement = document.getElementById('phase-title');
  const descriptionElement = document.getElementById('phase-description');
  const instructionsElement = document.getElementById('phase-instructions');
  
  // Update content
  if (titleElement) titleElement.textContent = phase.title;
  if (descriptionElement) descriptionElement.textContent = phase.description;
  if (instructionsElement) instructionsElement.textContent = phase.instructions;
  
  // Update button states
  if (prevButton) {
    prevButton.disabled = currentPhaseIndex === 0;
    prevButton.classList.toggle('opacity-50', currentPhaseIndex === 0);
    prevButton.classList.toggle('cursor-not-allowed', currentPhaseIndex === 0);
  }
  
  if (nextButton) {
    nextButton.disabled = currentPhaseIndex === TOURNAMENT_PHASES.length - 1;
    nextButton.classList.toggle('opacity-50', currentPhaseIndex === TOURNAMENT_PHASES.length - 1);
    nextButton.classList.toggle('cursor-not-allowed', currentPhaseIndex === TOURNAMENT_PHASES.length - 1);
  }
}

function updatePhaseContent() {
  const phase = TOURNAMENT_PHASES[currentPhaseIndex];
  const groupSelectorContainer = document.getElementById('group-selector-container');
  const standingsContainer = document.getElementById('standings-section');
  
  // Show/hide group selector based on phase
  if (groupSelectorContainer) {
    groupSelectorContainer.style.display = phase.showGroupSelector ? 'block' : 'none';
  }
  
  // Update content based on phase
  if (standingsContainer) {
    if (phase.id === 'groups') {
      // Load group standings if not already loaded
      standingsContainer.style.display = 'block';
      if (standingsGroupsData.length === 0) {
        getStandings();
      } else {
        renderEnhancedStandingsGroup(0);
      }
    } else {
      // Show phase-specific content for knockout stages
      standingsContainer.style.display = 'block';
      renderKnockoutPhase(phase);
    }
  }
}

async function renderKnockoutPhase(phase) {
  const container = document.getElementById('standings-section');
  if (!container) return;

  const roundMap = {
    round16: 'Round of 16',
    quarter: 'Quarter-finals',
    semi: 'Semi-finals',
    final: 'Final'
  };
  const roundName = roundMap[phase.id];

  if (!roundName) {
    container.innerHTML = `<p>Fase do torneio inválida: ${phase.id}</p>`;
    return;
  }

  // Fetch fixtures for the current phase
  const fixtures = await getFixtures(roundName);

  // Clear loading/error state set by getFixtures
  updateStandingsUI(false, false);

  if (!fixtures || fixtures.length === 0) {
    container.innerHTML = `
      <div class="w-full max-w-4xl bg-white rounded-lg border border-gray-300 shadow-lg p-8 text-center">
        <div class="mb-6">
          <div class="text-6xl mb-4">🏆</div>
          <h3 class="text-2xl font-bold text-gray-700 font-['Inter'] mb-2">${phase.title}</h3>
          <p class="text-gray-600 font-['Inter']">${phase.description}</p>
        </div>
        <div class="bg-gray-100 rounded-lg p-6 border border-gray-300">
          <div class="text-amber-600 font-semibold font-['Inter'] mb-2">📅 Em breve</div>
          <p class="text-gray-700 text-sm font-['Inter']">
            Os confrontos para esta fase ainda não foram definidos. As informações aparecerão aqui assim que estiverem disponíveis.
          </p>
        </div>
      </div>
    `;
    return;
  }

  const fixturesHTML = fixtures.map(fixture => {
    const date = new Date(fixture.fixture.date);
    const formattedDate = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    const formattedTime = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    const venue = fixture.fixture.venue.name || 'Estádio a definir';
    const city = fixture.fixture.venue.city || 'Cidade a definir';

    const scoreHome = fixture.goals.home !== null ? fixture.goals.home : '-';
    const scoreAway = fixture.goals.away !== null ? fixture.goals.away : '-';
    const isFinished = fixture.fixture.status.short === 'FT';

    return `
      <div class="grid grid-cols-1 md:grid-cols-3 items-center gap-4 p-4 border-b border-gray-200 last:border-b-0 hover:bg-gray-50 transition-colors duration-200">
        <!-- Time da Casa -->
        <div class="flex items-center gap-3 justify-end md:order-1">
          <span class="font-medium text-gray-800 text-sm text-right truncate">${fixture.teams.home.name}</span>
          <img src="${fixture.teams.home.logo}" alt="${fixture.teams.home.name}" class="w-8 h-8 rounded-full border object-cover bg-white" onerror="this.src='https://via.placeholder.com/32x32/cccccc/666666?text=?'">
        </div>
        
        <!-- Informações da Partida -->
        <div class="text-center md:order-2">
          <div class="font-bold text-xl text-gray-800">${scoreHome} &times; ${scoreAway}</div>
          <div class="text-xs text-gray-500 mt-1">${formattedDate} - ${formattedTime}</div>
          <div class="text-xs text-gray-500 truncate" title="${venue}, ${city}">${venue}</div>
          ${isFinished ? '<span class="text-xs font-bold text-red-600">Encerrado</span>' : ''}
        </div>
        
        <!-- Time Visitante -->
        <div class="flex items-center gap-3 justify-start md:order-3">
          <img src="${fixture.teams.away.logo}" alt="${fixture.teams.away.name}" class="w-8 h-8 rounded-full border object-cover bg-white" onerror="this.src='https://via.placeholder.com/32x32/cccccc/666666?text=?'">
          <span class="font-medium text-gray-800 text-sm text-left truncate">${fixture.teams.away.name}</span>
        </div>
      </div>
    `;
  }).join('');

  container.innerHTML = `
    <div class="w-full max-w-4xl bg-white rounded-lg border border-gray-300 shadow-lg overflow-hidden">
      <div class="px-6 py-4" style="background-color: #E9C043;">
        <h3 class="text-xl font-bold text-white font-['Inter'] flex items-center gap-3">
          <span class="text-2xl">⚔️</span>
          ${phase.title}
        </h3>
      </div>
      <div class="divide-y divide-gray-200">
        ${fixturesHTML}
      </div>
    </div>
  `;
}

// Inicialização principal - Aguarda DOM ou executa imediatamente se já carregado
async function initializePage() {
  // Configura dados globais primeiro
  await setupArtilheirosGlobal();
  
  // Depois renderiza os componentes
  getStandings();
  getFixtures('Round of 16'); // Pré-carrega os jogos das oitavas
  renderArtilheiros();
  renderRankingArtilheiros();
  renderTopscoreRankings();
  initializePhaseNavigation();
}

// Executa quando DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePage);
} else {
  // DOM já carregado, executa imediatamente
  initializePage();
}

// Drag-to-scroll para odds turbinadas
(function () {
  const oddsScroll = document.querySelector('.odds-scroll');
  let isDown = false;
  let startX;
  let scrollLeft;
  if (oddsScroll) {
    oddsScroll.addEventListener('mousedown', (e) => {
      isDown = true;
      oddsScroll.classList.add('active');
      startX = e.pageX - oddsScroll.offsetLeft;
      scrollLeft = oddsScroll.scrollLeft;
    });
    oddsScroll.addEventListener('mouseleave', () => {
      isDown = false;
      oddsScroll.classList.remove('active');
    });
    oddsScroll.addEventListener('mouseup', () => {
      isDown = false;
      oddsScroll.classList.remove('active');
    });
    oddsScroll.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - oddsScroll.offsetLeft;
      const walk = (x - startX) * 1.5; // scroll-fast
      oddsScroll.scrollLeft = scrollLeft - walk;
    });
    // Touch support
    oddsScroll.addEventListener('touchstart', (e) => {
      isDown = true;
      startX = e.touches[0].pageX - oddsScroll.offsetLeft;
      scrollLeft = oddsScroll.scrollLeft;
    });
    oddsScroll.addEventListener('touchend', () => {
      isDown = false;
    });
    oddsScroll.addEventListener('touchmove', (e) => {
      if (!isDown) return;
      const x = e.touches[0].pageX - oddsScroll.offsetLeft;
      const walk = (x - startX) * 1.5;
      oddsScroll.scrollLeft = scrollLeft - walk;
    });
  }
})();

// Betslip remover
document.addEventListener("DOMContentLoaded", () => {
  const observer = new MutationObserver(() => {
    const containerRight = document.querySelector(".view-widget-container-right");
    const betslipDesktop = document.querySelector(".betslip-desktop");
    if (containerRight && betslipDesktop) {
      document.body.appendChild(betslipDesktop);
      containerRight.style.display = "none";
      betslipDesktop.style.position = "fixed";
      betslipDesktop.style.bottom = "0";
      betslipDesktop.style.right = "0";
      betslipDesktop.style.zIndex = "9999";
      betslipDesktop.style.maxHeight = "90vh";
      betslipDesktop.style.overflowY = "auto";
      betslipDesktop.style.display = "block";
      betslipDesktop.style.background = "white";
      betslipDesktop.style.boxShadow = "0 0 10px rgba(0,0,0,0.3)";
      observer.disconnect();
    }
  });
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
});

// Enhanced standings display with better error handling and UI feedback
function updateStandingsUI(isLoading = false, hasError = false, errorMessage = '') {
  const container = document.getElementById('standings-section');
  const loadingElement = document.getElementById('standings-loading');
  
  if (!container) return;
  if (isLoading) {
    container.innerHTML = `
      <div class="flex items-center justify-center py-12">
        <div class="text-gray-700 text-center">
          <div class="loading-spinner mx-auto mb-4"></div>
          <p class="text-lg font-['Inter']">${errorMessage || 'Carregando...'}</p>
        </div>
      </div>
    `;
    return;
  }
  if (hasError) {
    container.innerHTML = `
      <div class="w-full max-w-4xl bg-white rounded-lg border border-gray-300 shadow-lg p-8 text-center">
        <div class="text-red-500 text-4xl mb-4">⚠️</div>
        <h3 class="text-xl font-bold text-gray-700 font-['Inter'] mb-2">Erro ao carregar dados</h3>
        <p class="text-gray-600 font-['Inter']">${errorMessage}</p>
      </div>
    `;
    return;
  }
}

// Enhanced function to render group standings with dark theme styling
function renderEnhancedStandingsGroup(groupIndex) {
  const container = document.getElementById('standings-section');
  if (!container) {
    console.error('Container #standings-section não encontrado');
    return;
  }
  
  container.innerHTML = '';
  if (!standingsGroupsData || !Array.isArray(standingsGroupsData) || 
      !standingsGroupsData[groupIndex] || !standingsGroupsData[groupIndex].length) {
    container.innerHTML = `
      <div class="w-full max-w-4xl bg-white rounded-lg border border-gray-300 shadow-lg p-8 text-center">
        <div class="text-gray-400 text-4xl mb-4">📊</div>
        <h3 class="text-xl font-bold text-gray-700 font-['Inter'] mb-2">Nenhum dado disponível</h3>
        <p class="text-gray-600 font-['Inter']">Nenhum dado disponível para este grupo.</p>
      </div>
    `;
    return;
  }
  
  const groupArr = standingsGroupsData[groupIndex];
  // Format group name properly in Portuguese
  let groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + groupIndex)}`;
  
  // Convert "Group A" to "Grupo A" if needed
  if (groupName.startsWith('Group ')) {
    groupName = groupName.replace('Group ', 'Grupo ');
  }
  const tableHTML = `
    <div class="w-full max-w-4xl bg-white rounded-lg border border-gray-300 shadow-lg overflow-hidden">      <!-- Header com estilo similar aos artilheiros -->
      <div class="px-6 py-4" style="background-color: #E9C043;">
        <h3 class="text-xl font-bold text-white font-['Inter'] flex items-center gap-3">
          <span class="text-2xl">🏆</span>
          ${groupName}
        </h3>
      </div>
      
      <!-- Cabeçalho da tabela com bordas lineares -->
      <div class="w-full bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-300">
        <div class="grid grid-cols-10 gap-1 px-4 py-3">
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">Pos</div>
          <div class="col-span-3 text-left text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">Time</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">Pts</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">J</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">V</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">E</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">D</div>
          <div class="text-center text-xs font-bold text-gray-700 font-['Inter'] uppercase tracking-wider">SG</div>
        </div>
      </div>
      
      <!-- Corpo da tabela com bordas lineares -->
      <div class="w-full bg-white">
        ${groupArr.map((team, index) => {          const isQualified = index < 2; // Top 2 teams qualify
          const isTop1 = index === 0;
          const rowClass = isQualified ? 'hover:bg-green-50' : 'hover:bg-gray-50';
          const positionClass = isTop1 ? 'font-bold' : isQualified ? 'text-green-700 font-bold' : 'text-gray-700';
          const positionColor = isTop1 ? '#E9C043' : isQualified ? '#059669' : '#374151';
          
          return `            <div class="grid grid-cols-10 gap-1 px-4 py-4 border-b border-gray-200 ${rowClass} transition-colors duration-200">
              <div class="text-center ${positionClass} font-semibold font-['Inter'] text-sm" style="color: ${positionColor};">${team.rank}</div>
              <div class="col-span-3 flex items-center gap-3">
                <img src="${team.team.logo}" 
                     alt="${team.team.name}" 
                     class="w-8 h-8 rounded border border-gray-300 object-cover bg-white" 
                     onerror="this.src='https://via.placeholder.com/32x32/cccccc/666666?text=?'" />
                <div class="flex flex-col min-w-0">
                  <span class="font-medium text-gray-900 text-sm font-['Inter'] truncate">${team.team.name}</span>
                  ${isQualified ? '<span class="text-xs text-green-600 font-semibold font-[\'Inter\']">Qualificado</span>' : ''}
                </div>
              </div>
              <div class="text-center font-bold font-['Inter'] text-sm" style="color: #E9C043;">${team.points}</div>
              <div class="text-center text-gray-600 font-['Inter'] text-sm">${team.all.played}</div>
              <div class="text-center text-green-600 font-['Inter'] text-sm font-medium">${team.all.win}</div>
              <div class="text-center text-yellow-600 font-['Inter'] text-sm font-medium">${team.all.draw}</div>
              <div class="text-center text-red-600 font-['Inter'] text-sm font-medium">${team.all.lose}</div>
              <div class="text-center font-medium font-['Inter'] text-sm ${team.goalsDiff >= 0 ? 'text-green-600' : 'text-red-600'}">
                ${team.goalsDiff >= 0 ? '+' : ''}${team.goalsDiff}
              </div>
            </div>
          `;
        }).join('')}
      </div>
      
      <!-- Footer com informações usando fonte Inter -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-300">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 text-xs text-gray-600 font-['Inter']">
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-green-500 rounded"></div>
            <span class="font-medium">Classificado para próxima fase</span>
          </div>
          <div class="text-gray-500 text-xs">
            J = Jogos | V = Vitórias | E = Empates | D = Derrotas | SG = Saldo de Gols
          </div>
        </div>
      </div>
    </div>
  `;
  
  container.innerHTML = tableHTML;
  console.log(`Tabela de classificacao renderizada para ${groupName}`);
}

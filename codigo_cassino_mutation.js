<script defer>
    (function () {
        'use strict';

        console.log('Script initialization started');
        const targetBasePath = '/casino';
        let isUpdating = false;
        let currentPath = window.location.pathname;
        console.log('Initial path:', currentPath);

        function isCasinoPath(path) {
            return path.includes('/casino');
        }

        function updateRegularNavigation() {
            console.log('updateRegularNavigation called');
            if (isUpdating) {
                console.log('Already updating, skipping');
                return;
            }
            isUpdating = true;
            console.log('Starting regular navigation update');

            const navItems = document.querySelectorAll('.bottom-nav-item');
            console.log('Found nav items:', navItems.length);
            
            const cupomItem = Array.from(navItems).find(item => {
                const label = item.querySelector('.bottom-nav-item-label');
                return label && label.textContent.trim() === 'Cupom';
            });

            if (cupomItem && !cupomItem.classList.contains('hidden')) {
                console.log('Found Cupom item, updating navigation');
                cupomItem.classList.add('hidden');
                
                const apostasNav = document.createElement('div');
                apostasNav.className = 'bottom-nav-item flex flex-col items-center justify-center space-y-1 w-full h-full whitespace-nowrap overflow-hidden';
                apostasNav.innerHTML = `
                    <a class="flex flex-col items-center gap-1 justify-center" href="/account/bets">
                        <svg width="20" height="20" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.99967 6.45335C9.19967 4.45335 10.333 3.42668 10.333 2.12668C10.333 1.12668 9.49967 0.333351 8.49967 0.333351C7.92634 0.333351 7.37967 0.573351 6.99967 1.00002C6.61967 0.573351 6.07301 0.333351 5.45967 0.333351C4.45967 0.333351 3.66634 1.16668 3.66634 2.16668C3.66634 3.42668 4.79967 4.45335 6.99967 6.45335ZM6.45301 7.00002C4.45301 4.80002 3.42634 3.66668 2.12634 3.66668C1.12634 3.66668 0.333008 4.50002 0.333008 5.50002C0.333008 6.07335 0.573008 6.62002 0.999674 7.00002C0.573008 7.38002 0.333008 7.92668 0.333008 8.54002C0.333008 9.54002 1.16634 10.3334 2.16634 10.3334C3.42634 10.3334 4.45301 9.20002 6.45301 7.00002ZM7.55301 7.00002C9.54634 9.20002 10.573 10.3334 11.873 10.3334C12.873 10.3334 13.6663 9.50002 13.6663 8.50002C13.6663 7.92668 13.4263 7.38002 12.9997 7.00002C13.4263 6.62002 13.6663 6.07335 13.6663 5.46002C13.6663 4.46002 12.833 3.66668 11.833 3.66668C10.573 3.66668 9.54634 4.80002 7.55301 7.00002ZM6.99967 7.54668C4.79967 9.54668 3.66634 10.5734 3.66634 11.8734C3.66634 12.8734 4.49967 13.6667 5.49967 13.6667C6.07301 13.6667 6.61967 13.4267 6.99967 13C7.37967 13.4267 7.92634 13.6667 8.53967 13.6667C9.53967 13.6667 10.333 12.8334 10.333 11.8334C10.333 10.5734 9.19967 9.54668 6.99967 7.54668Z" fill="white"></path>
                        </svg>
                        <span class="bottom-nav-item-label text-xxs font-medium">Apostas</span>
                    </a>
                `;
                cupomItem.parentNode.insertBefore(apostasNav, cupomItem.nextSibling);
            }

            isUpdating = false;
            console.log('Regular navigation update completed');
        }

        function updateCasinoNavigation() {
            console.log('updateCasinoNavigation called');
            if (isUpdating) {
                console.log('Already updating, skipping');
                return;
            }
            isUpdating = true;
            console.log('Starting casino navigation update');

            const navItems = document.querySelectorAll('.bottom-nav-item');
            console.log('Found nav items:', navItems.length);
            
            // Primeiro, esconder os itens que não devem aparecer no cassino
            navItems.forEach(item => {
                const label = item.querySelector('.bottom-nav-item-label');
                if (label && (label.textContent.trim() === 'Cupom' || label.textContent.trim() === 'Ao vivo')) {
                    console.log('Hiding item:', label.textContent.trim());
                    item.classList.add('hidden');
                }
            });

            // Verificar se os botões do cassino já existem
            const existingSlots = Array.from(navItems).find(item => {
                const label = item.querySelector('.bottom-nav-item-label');
                return label && label.textContent.trim() === 'Slots';
            });

            const existingCrash = Array.from(navItems).find(item => {
                const label = item.querySelector('.bottom-nav-item-label');
                return label && label.textContent.trim() === 'Crash';
            });

            const cassinoNav = Array.from(navItems).find(item => {
                const label = item.querySelector('.bottom-nav-item-label');
                return label && label.textContent.trim() === 'Cassino';
            });

            if (cassinoNav) {
                console.log('Found Cassino item, checking for existing navigation items');
                
                // Adicionar Slots apenas se não existir
                if (!existingSlots) {
                    console.log('Adding Slots navigation item');
                    const slotsNav = document.createElement('div');
                    slotsNav.className = 'bottom-nav-item flex flex-col items-center justify-center space-y-1 w-full h-full whitespace-nowrap overflow-hidden';
                    slotsNav.innerHTML = `
                        <svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="slot-machine" class="svg-inline--fa fa-slot-machine bottom-nav-item-icon text-xl" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                            <path fill="currentColor" d="M400 416l-288 0 0 32c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-32z"></path>
                        </svg>
                        <span class="bottom-nav-item-label text-xxs font-medium">Slots</span>
                    `;
                    cassinoNav.parentNode.insertBefore(slotsNav, cassinoNav);
                }

                // Adicionar Crash apenas se não existir
                if (!existingCrash) {
                    console.log('Adding Crash navigation item');
                    const crashNav = document.createElement('div');
                    crashNav.className = 'bottom-nav-item flex flex-col items-center justify-center space-y-1 w-full h-full whitespace-nowrap overflow-hidden';
                    crashNav.innerHTML = `
                        <svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="chart-line" class="svg-inline--fa fa-chart-line bottom-nav-item-icon text-xl" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path fill="currentColor" d="M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64V400c0 44.2 35.8 80 80 80H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H80c-8.8 0-16-7.2-16-16V64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"></path>
                        </svg>
                        <span class="bottom-nav-item-label text-xxs font-medium">Crash</span>
                    `;
                    cassinoNav.parentNode.insertBefore(crashNav, cassinoNav.nextSibling);
                }
            }

            isUpdating = false;
            console.log('Casino navigation update completed');
        }

        function handleNavigation() {
            console.log('handleNavigation called');
            const newPath = window.location.pathname;
            console.log('New path:', newPath, 'Current path:', currentPath);
            
            if (newPath === currentPath) {
                console.log('Path unchanged, skipping');
                return;
            }
            
            currentPath = newPath;
            console.log('Path changed to:', currentPath);

            if (!isCasinoPath(currentPath)) {
                console.log('Path does not contain /casino - updating regular navigation');
                updateRegularNavigation();
            } else {
                console.log('Path contains /casino - updating casino navigation');
                //updateCasinoNavigation();
            }
        }

        console.log('Setting up MutationObserver');
        const observer = new MutationObserver((mutations) => {
            console.log('MutationObserver triggered');
            if (isUpdating) {
                console.log('Already updating, skipping observer');
                return;
            }
            console.log('Current path in observer:', currentPath);
            if (isCasinoPath(currentPath)) {
                console.log('Observer: updating casino navigation');
                //updateCasinoNavigation();
            } else {
                console.log('Observer: updating regular navigation');
                updateRegularNavigation();
            }
        });

        observer.observe(document.body, { 
            childList: true, 
            subtree: true,
            attributes: true,
            attributeFilter: ['class']
        });

        console.log('Running initial navigation setup');
        handleNavigation();

        console.log('Setting up popstate listener');
        window.addEventListener('popstate', () => {
            console.log('popstate event triggered');
            handleNavigation();
        });

        console.log('Setting up interval check');
        setInterval(() => {
            if (window.location.pathname !== currentPath) {
                console.log('Interval check detected path change');
                handleNavigation();
            }
        }, 100);

        console.log('Script initialization completed');
    })();
</script>
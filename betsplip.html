<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div
    class="view-widget-container-right flex sticky top-[var(--header-height)] w-widgetContainerRight h-[calc(var(--doc-height)-var(--header-height))] pb-[106px] bg-contentRight text-contentRightContrast flex-shrink-0 border-l-[1px] border-neutral overflow-hidden">
    <div class="widget-container flex flex-grow flex-col space-y-6 p-4 overflow-auto ">
        <div class="betslip-desktop fixed bottom-0 -ml-2 w-[384px] shadow-card z-[25]">
            <div
                class="betslip-inner flex flex-col flex-grow md:max-w-[500px] bg-betslip text-betslipContrast overflow-hidden relative pointer-events-auto betslip-inner-desktop max-h-[75vh] shadow-lg border-[1px] border-neutral rounded-t-md">
                <div class="betslip-header flex flex-col flex-shrink-0 bg-betslipHeader text-betslipHeaderContrast">
                    <div
                        class="betslip-header-inner flex justify-between items-center px-2 h-[52px] border-b-[1px] border-neutral">
                        <div class="betslip-header-inner-clear flex justify-center items-center h-full aspect-square">
                            <div class="icon-button-wrapper flex  "><button title="Clear betslip" class="button flex justify-center items-center space-x-2 whitespace-nowrap text-sm font-semibold border-[1px] box-border transition-all duration-300 outline-none cursor-pointer hover:brightness-125  
          icon-button  w-8 h-8 bg-none border-none text-inherit" type="button"><svg aria-hidden="true"
                                        focusable="false" data-prefix="far" data-icon="trash-can"
                                        class="svg-inline--fa fa-trash-can icon-button-icon" role="img"
                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                        <path fill="currentColor"
                                            d="M170.5 51.6L151.5 80l145 0-19-28.4c-1.5-2.2-4-3.6-6.7-3.6l-93.7 0c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6L354.2 80 368 80l48 0 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-8 0 0 304c0 44.2-35.8 80-80 80l-224 0c-44.2 0-80-35.8-80-80l0-304-8 0c-13.3 0-24-10.7-24-24S10.7 80 24 80l8 0 48 0 13.8 0 36.7-55.1C140.9 9.4 158.4 0 177.1 0l93.7 0c18.7 0 36.2 9.4 46.6 24.9zM80 128l0 304c0 17.7 14.3 32 32 32l224 0c17.7 0 32-14.3 32-32l0-304L80 128zm80 64l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16z">
                                        </path>
                                    </svg></button></div>
                        </div>
                        <div
                            class="betslip-header-inner-center flex flex-grow space-x-2 items-center h-full justify-center cursor-pointer">
                            <span class="betslip-header-inner-center-label text-lg font-semibold">Cupom</span></div>
                        <div class="betslip-header-inner-close flex justify-center items-center h-full aspect-square">
                            <div class="icon-button-wrapper flex  "><button title="Close betslip" class="button flex justify-center items-center space-x-2 whitespace-nowrap text-sm font-semibold border-[1px] box-border transition-all duration-300 outline-none cursor-pointer hover:brightness-125  
          icon-button  w-8 h-8 bg-none border-none text-inherit" type="button"><svg aria-hidden="true"
                                        focusable="false" data-prefix="far" data-icon="chevron-up"
                                        class="svg-inline--fa fa-chevron-up icon-button-icon" role="img"
                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                        <path fill="currentColor"
                                            d="M239 111c9.4-9.4 24.6-9.4 33.9 0L465 303c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0l-175-175L81 337c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9L239 111z">
                                        </path>
                                    </svg></button></div>
                        </div>
                    </div>
                </div>
                <div class="betslip-inner-content-wrapper flex flex-grow flex-col w-full overflow-hidden ">
                    <div class="betslip-inner-content flex flex-grow flex-col overflow-hidden relative "></div>
                    <div class="betslip-footer flex justify-center w-full transition-all "><a
                            class="betslip-signin-to-bet w-full"
                            href="/br/account/login?redirectTo=/copa-do-mundo-clubes"><button class="button flex justify-center items-center space-x-2 whitespace-nowrap text-sm font-semibold border-[1px] box-border transition-all duration-300 outline-none cursor-pointer hover:brightness-125 px-4 py-2 
         w-full contained-button bg-secondary text-secondaryContrast border-none betslip-button h-[52px]"
                                type="button">
                                <div class="betslip-button-inner flex flex-col justify-center items-center"><span
                                        class="betslip-button-title text-lg font-semibold leading-5">Faça o login para
                                        apostar</span></div>
                            </button></a></div>
                </div>
            </div>
        </div>
    </div>
</div>




  <!-- Betslip remover --->
  <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DEBUG: DOM carregado, iniciando reorganização do betslip...');
            
            // Função para reorganizar o container do betslip
            function reorganizeBetslipContainer() {
                console.log('📋 DEBUG: Iniciando reorganizeBetslipContainer()');
                
                // Captura os elementos necessários
                const mainContainer = document.querySelector('.view-widget-container-main');
                const rightContainer = document.querySelector('.view-widget-container-right');
                const betslip = document.querySelector('.betslip-desktop');
                
                console.log('🔍 DEBUG: Elementos encontrados:');
                console.log('  - mainContainer:', mainContainer ? '✅ Encontrado' : '❌ Não encontrado');
                console.log('  - rightContainer:', rightContainer ? '✅ Encontrado' : '❌ Não encontrado');
                console.log('  - betslip:', betslip ? '✅ Encontrado' : '❌ Não encontrado');
                
                if (mainContainer && rightContainer && betslip) {
                    console.log('✨ DEBUG: Todos os elementos encontrados, procedendo com a reorganização...');
                    
                    // Remove o container direito da sua posição atual
                    rightContainer.remove();
                    console.log('🗑️ DEBUG: Container direito removido');
                    
                    // Modifica o estilo do container principal para não considerar o espaço do widget direito
                    mainContainer.style.width = '100%';
                    mainContainer.classList.remove('xl:w-[calc(100%-var(--widget-container-right-width))]');
                    console.log('📐 DEBUG: Largura do container principal alterada para 100%');
                    
                    // Cria um novo container sticky dentro do container principal
                    const stickyContainer = document.createElement('div');
                    stickyContainer.className = 'betslip-sticky-container';
                    stickyContainer.style.cssText = `
                        position: sticky;
                        top: 20px;
                        right: 20px;
                        width: 0;
                        height: 0;
                        overflow: visible;
                        z-index: 1000;
                        pointer-events: none;
                        float: right;
                        margin-top: 20px;
                        margin-right: 20px;
                    `;
                    console.log('📦 DEBUG: Container sticky criado');
                    
                    // Modifica o betslip para ficar flutuante
                    betslip.style.cssText = `
                        position: fixed;
                        top: auto;
                        bottom: 20px;
                        right: 20px;
                        width: 384px;
                        margin-left: 0;
                        z-index: 1000;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        pointer-events: auto;
                        border-radius: 8px;
                        overflow: hidden;
                    `;
                    console.log('🎨 DEBUG: Estilos do betslip aplicados');
                    
                    // Adiciona o betslip ao novo container sticky
                    stickyContainer.appendChild(betslip);
                    console.log('🔗 DEBUG: Betslip adicionado ao container sticky');
                    
                    // Adiciona o container sticky ao container principal
                    mainContainer.appendChild(stickyContainer);
                    console.log('🔗 DEBUG: Container sticky adicionado ao container principal');
                    
                    // Melhora o estilo do betslip interno
                    const betslipInner = betslip.querySelector('.betslip-inner');
                    if (betslipInner) {
                        betslipInner.style.borderRadius = '8px';
                        betslipInner.style.maxHeight = '80vh';
                        console.log('✅ DEBUG: Estilos do betslip interno aplicados');
                    } else {
                        console.log('⚠️ DEBUG: betslip-inner não encontrado');
                    }
                    
                    console.log('🎉 DEBUG: Betslip reorganizado com sucesso!');
                } else {
                    console.log('❌ DEBUG: Falha na reorganização - elementos não encontrados');
                    if (!mainContainer) console.log('  ❌ mainContainer não encontrado');
                    if (!rightContainer) console.log('  ❌ rightContainer não encontrado');
                    if (!betslip) console.log('  ❌ betslip não encontrado');
                }
            }
            
            // Função para adicionar funcionalidade de minimizar/maximizar
            function setupBetslipToggle() {
                console.log('🔄 DEBUG: Iniciando setupBetslipToggle()');
                
                const betslipToggleBtn = document.querySelector('.betslip-header-inner-close');
                const betslipContent = document.querySelector('.betslip-inner-content-wrapper');
                
                console.log('🔍 DEBUG: Elementos de toggle encontrados:');
                console.log('  - betslipToggleBtn:', betslipToggleBtn ? '✅ Encontrado' : '❌ Não encontrado');
                console.log('  - betslipContent:', betslipContent ? '✅ Encontrado' : '❌ Não encontrado');
                
                if (betslipToggleBtn && betslipContent) {
                    betslipToggleBtn.addEventListener('click', function() {
                        console.log('🖱️ DEBUG: Botão de toggle clicado');
                        if (betslipContent.style.display === 'none') {
                            betslipContent.style.display = 'flex';
                            const icon = betslipToggleBtn.querySelector('svg');
                            if (icon) icon.style.transform = 'rotate(0deg)';
                            console.log('📖 DEBUG: Betslip expandido');
                        } else {
                            betslipContent.style.display = 'none';
                            const icon = betslipToggleBtn.querySelector('svg');
                            if (icon) icon.style.transform = 'rotate(180deg)';
                            console.log('📕 DEBUG: Betslip minimizado');
                        }
                    });
                    console.log('✅ DEBUG: Event listener do botão de toggle adicionado');
                }
                
                // Permite clicar no cabeçalho para minimizar/maximizar
                const betslipHeader = document.querySelector('.betslip-header-inner-center');
                console.log('🔍 DEBUG: betslipHeader:', betslipHeader ? '✅ Encontrado' : '❌ Não encontrado');
                
                if (betslipHeader && betslipContent) {
                    betslipHeader.style.cursor = 'pointer';
                    betslipHeader.addEventListener('click', function() {
                        console.log('🖱️ DEBUG: Cabeçalho do betslip clicado');
                        if (betslipContent.style.display === 'none') {
                            betslipContent.style.display = 'flex';
                            const icon = document.querySelector('.betslip-header-inner-close svg');
                            if (icon) icon.style.transform = 'rotate(0deg)';
                            console.log('📖 DEBUG: Betslip expandido via cabeçalho');
                        } else {
                            betslipContent.style.display = 'none';
                            const icon = document.querySelector('.betslip-header-inner-close svg');
                            if (icon) icon.style.transform = 'rotate(180deg)';
                            console.log('📕 DEBUG: Betslip minimizado via cabeçalho');
                        }
                    });
                    console.log('✅ DEBUG: Event listener do cabeçalho adicionado');
                }
                
                console.log('🎉 DEBUG: setupBetslipToggle() concluído!');
            }
            
            // Função para remover o betslip da página, tentando até encontrar e remover
            function removerBetslipTentando() {
                let tentativas = 0;
                const maxTentativas = 20;
                const intervalo = 300; // ms

                function tentarRemover() {
                    const betslip = document.querySelector('.betslip-desktop');
                    if (betslip) {
                        betslip.remove();
                        console.log('🗑️ DEBUG: Betslip removido da página!');
                    } else if (tentativas < maxTentativas) {
                        tentativas++;
                        console.log(`⏳ DEBUG: Tentativa ${tentativas} de remover betslip...`);
                        setTimeout(tentarRemover, intervalo);
                    } else {
                        console.log('❌ DEBUG: Não foi possível encontrar o betslip para remover após várias tentativas.');
                    }
                }
                tentarRemover();
            }
            
            // Executa as funções após o DOM estar carregado
            console.log('🏁 DEBUG: Executando funções principais...');
            // reorganizeBetslipContainer(); // Desabilitado para não reorganizar
            // setupBetslipToggle(); // Desabilitado para não adicionar toggle
            removerBetslipTentando();
            console.log('🏆 DEBUG: Script de remoção do betslip finalizado!');
        });
  </script>
 
</body>
</html>
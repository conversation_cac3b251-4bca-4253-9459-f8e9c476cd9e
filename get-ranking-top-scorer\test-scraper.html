<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Globo Esporte Scraper</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">🔍 Teste do Globo Esporte Scraper</h1>
            <p class="text-gray-600 mb-6">Esta página demonstra como o scraper funciona para extrair dados do Mundial de Clubes do Globo Esporte.</p>
            
            <div class="flex gap-4 mb-6">
                <button id="test-scraper" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    🚀 Testar Scraper
                </button>
                <button id="clear-cache" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    🗑️ Limpar Cache
                </button>
                <button id="load-cache" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    📦 Carregar do Cache
                </button>
            </div>
            
            <!-- Status do Teste -->
            <div id="status" class="mb-6 p-4 rounded-lg bg-gray-50">
                <h3 class="text-lg font-semibold mb-2">Status:</h3>
                <div id="status-content" class="text-gray-600">Pronto para testar...</div>
            </div>
        </div>

        <!-- Resultados -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Artilheiros -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">🥅 Artilheiros</h2>
                <div id="artilheiros-container" class="space-y-3">
                    <p class="text-gray-500">Execute o teste para ver os dados...</p>
                </div>
            </div>

            <!-- Classificação -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">🏆 Classificação</h2>
                <div id="classificacao-container" class="space-y-3">
                    <p class="text-gray-500">Execute o teste para ver os dados...</p>
                </div>
            </div>

            <!-- Jogos -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">⚽ Próximos Jogos</h2>
                <div id="jogos-container" class="space-y-3">
                    <p class="text-gray-500">Execute o teste para ver os dados...</p>
                </div>
            </div>

            <!-- Notícias -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">📰 Notícias</h2>
                <div id="noticias-container" class="space-y-3">
                    <p class="text-gray-500">Execute o teste para ver os dados...</p>
                </div>
            </div>
        </div>

        <!-- Dados Brutos (JSON) -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">📋 Dados Brutos (JSON)</h2>
            <div id="raw-data" class="json-viewer">
                // Os dados JSON aparecerão aqui após executar o teste
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="globo-esporte-scraper.js"></script>
    
    <script>
        class ScraperTester {
            constructor() {
                this.scraper = new GloboEsporteScraper();
                this.initEventListeners();
            }

            initEventListeners() {
                document.getElementById('test-scraper').addEventListener('click', () => this.testScraper());
                document.getElementById('clear-cache').addEventListener('click', () => this.clearCache());
                document.getElementById('load-cache').addEventListener('click', () => this.loadFromCache());
            }

            async testScraper() {
                this.setStatus('🔄 Executando scraper...', 'loading');
                this.disableButtons(true);

                try {
                    const startTime = Date.now();
                    const data = await this.scraper.getData(false); // Force fresh data
                    const duration = Date.now() - startTime;

                    this.setStatus(`✅ Scraper executado com sucesso em ${duration}ms`, 'success');
                    this.displayResults(data);
                    this.displayRawData(data);

                } catch (error) {
                    this.setStatus(`❌ Erro ao executar scraper: ${error.message}`, 'error');
                    console.error('Erro no teste:', error);
                } finally {
                    this.disableButtons(false);
                }
            }

            clearCache() {
                localStorage.removeItem('globo_mundial_data');
                localStorage.removeItem('globo_mundial_timestamp');
                this.setStatus('🗑️ Cache limpo com sucesso', 'info');
            }

            async loadFromCache() {
                const cachedData = this.scraper.loadFromCache();
                if (cachedData) {
                    this.setStatus('📦 Dados carregados do cache', 'info');
                    this.displayResults(cachedData);
                    this.displayRawData(cachedData);
                } else {
                    this.setStatus('❌ Nenhum dado encontrado no cache', 'error');
                }
            }

            setStatus(message, type) {
                const statusContent = document.getElementById('status-content');
                const statusContainer = document.getElementById('status');
                
                statusContent.innerHTML = message;
                
                // Remove classes anteriores
                statusContainer.classList.remove('bg-blue-50', 'bg-green-50', 'bg-red-50', 'bg-yellow-50');
                
                // Adiciona classe baseada no tipo
                switch(type) {
                    case 'loading':
                        statusContainer.classList.add('bg-blue-50');
                        statusContent.innerHTML = `<div class="loading"></div> ${message}`;
                        break;
                    case 'success':
                        statusContainer.classList.add('bg-green-50');
                        break;
                    case 'error':
                        statusContainer.classList.add('bg-red-50');
                        break;
                    case 'info':
                        statusContainer.classList.add('bg-yellow-50');
                        break;
                    default:
                        statusContainer.classList.add('bg-gray-50');
                }
            }

            displayResults(data) {
                this.displayArtilheiros(data.artilheiros);
                this.displayClassificacao(data.classificacao);
                this.displayJogos(data.jogos);
                this.displayNoticias(data.noticias);
            }

            displayArtilheiros(artilheiros) {
                const container = document.getElementById('artilheiros-container');
                
                if (!artilheiros || artilheiros.length === 0) {
                    container.innerHTML = '<p class="text-gray-500">Nenhum artilheiro encontrado</p>';
                    return;
                }

                container.innerHTML = artilheiros.map((scorer, index) => `
                    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center font-bold">
                            ${scorer.position || index + 1}
                        </div>
                        <img src="${scorer.image}" alt="${scorer.name}" class="w-12 h-12 rounded-full object-cover" 
                             onerror="this.src='https://via.placeholder.com/48x48/E9C043/000000?text=${scorer.name.charAt(0)}'">
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800">${scorer.name}</div>
                            <div class="text-sm text-gray-600">${scorer.team}</div>
                        </div>
                        <div class="text-xl font-bold text-amber-600">${scorer.goals}</div>
                    </div>
                `).join('');
            }

            displayClassificacao(classificacao) {
                const container = document.getElementById('classificacao-container');
                
                if (!classificacao || classificacao.length === 0) {
                    container.innerHTML = '<p class="text-gray-500">Nenhuma classificação encontrada</p>';
                    return;
                }

                container.innerHTML = classificacao.map(group => `
                    <div class="mb-4">
                        <h3 class="font-bold text-lg text-gray-800 mb-2">${group.group}</h3>
                        <div class="space-y-1">
                            ${group.teams.map(team => `
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-2">
                                        <span class="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                                            ${team.position}
                                        </span>
                                        <span class="font-medium">${team.name}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-bold">${team.points} pts</div>
                                        <div class="text-xs text-gray-600">${team.played}J ${team.wins}V ${team.draws}E ${team.losses}D</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('');
            }

            displayJogos(jogos) {
                const container = document.getElementById('jogos-container');
                
                if (!jogos || jogos.length === 0) {
                    container.innerHTML = '<p class="text-gray-500">Nenhum jogo encontrado</p>';
                    return;
                }

                container.innerHTML = jogos.slice(0, 5).map(match => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="text-sm text-gray-600">
                            <div>${match.date}</div>
                            <div>${match.time}</div>
                        </div>
                        <div class="flex-1 text-center">
                            <div class="font-medium">
                                ${match.homeTeam} ${match.score ? match.score : 'vs'} ${match.awayTeam}
                            </div>
                        </div>
                        <div class="text-xs px-2 py-1 rounded ${this.getStatusClass(match.status)}">
                            ${match.status}
                        </div>
                    </div>
                `).join('');
            }

            displayNoticias(noticias) {
                const container = document.getElementById('noticias-container');
                
                if (!noticias || noticias.length === 0) {
                    container.innerHTML = '<p class="text-gray-500">Nenhuma notícia encontrada</p>';
                    return;
                }

                container.innerHTML = noticias.slice(0, 3).map(news => `
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-1">${news.title}</h4>
                        ${news.summary ? `<p class="text-sm text-gray-600 mb-2">${news.summary}...</p>` : ''}
                        ${news.link ? `<a href="${news.link}" target="_blank" class="text-blue-500 hover:text-blue-600 text-xs">Ler mais →</a>` : ''}
                    </div>
                `).join('');
            }

            displayRawData(data) {
                const container = document.getElementById('raw-data');
                container.textContent = JSON.stringify(data, null, 2);
            }

            getStatusClass(status) {
                switch(status?.toLowerCase()) {
                    case 'finalizado':
                        return 'bg-green-200 text-green-800';
                    case 'ao vivo':
                        return 'bg-red-200 text-red-800';
                    case 'agendado':
                        return 'bg-blue-200 text-blue-800';
                    default:
                        return 'bg-gray-200 text-gray-800';
                }
            }

            disableButtons(disabled) {
                const buttons = ['test-scraper', 'clear-cache', 'load-cache'];
                buttons.forEach(id => {
                    document.getElementById(id).disabled = disabled;
                });
            }
        }

        // Inicializa o testador quando a página carregar
        document.addEventListener('DOMContentLoaded', () => {
            const tester = new ScraperTester();
            console.log('🧪 Testador do scraper inicializado');
        });
    </script>
</body>
</html>

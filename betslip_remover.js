// Script para remover completamente o betslip da página
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DEBUG: DOM carregado, iniciando remoção do betslip...');
    
    // Função para remover completamente o betslip da página
    function removeBetslipCompletely() {
        console.log('📋 DEBUG: Iniciando removeBetslipCompletely()');
        
        // Captura os elementos necessários
        const mainContainer = document.querySelector('.view-widget-container-main');
        const rightContainer = document.querySelector('.view-widget-container-right');
        const betslip = document.querySelector('.betslip-desktop');
        
        console.log('🔍 DEBUG: Elementos encontrados:');
        console.log('  - mainContainer:', mainContainer ? '✅ Encontrado' : '❌ Não encontrado');
        console.log('  - rightContainer:', rightContainer ? '✅ Encontrado' : '❌ Não encontrado');
        console.log('  - betslip:', betslip ? '✅ Encontrado' : '❌ Não encontrado');
        
        // Remove o container direito
        if (rightContainer) {
            rightContainer.remove();
            console.log('🗑️ DEBUG: Container direito (view-widget-container-right) removido completamente');
        }
        
        // Remove o betslip diretamente se ele existe em qualquer lugar da página
        if (betslip) {
            betslip.remove();
            console.log('🗑️ DEBUG: Betslip removido completamente');
        }
        
        // Modifica o estilo do container principal para ocupar 100% da largura
        if (mainContainer) {
            mainContainer.style.width = '100%';
            mainContainer.classList.remove('xl:w-[calc(100%-var(--widget-container-right-width))]');
            console.log('📐 DEBUG: Largura do container principal alterada para 100%');
            console.log('✅ DEBUG: Container principal ajustado para usar toda a largura disponível');
        } else {
            console.log('⚠️ DEBUG: Container principal não encontrado, não foi possível ajustar a largura');
        }
        
        // Verificação final para garantir que tudo foi removido
        const betslipCheck = document.querySelector('.betslip-desktop, .betslip-inner, .betslip-sticky-container');
        if (!betslipCheck) {
            console.log('✅ DEBUG: Confirmado que o betslip foi completamente removido da página');
        } else {
            console.log('⚠️ DEBUG: Ainda existem elementos do betslip na página que não foram removidos');
            if (betslipCheck) {
                betslipCheck.remove();
                console.log('🗑️ DEBUG: Elementos restantes do betslip foram removidos');
            }
        }
    }
    
    // Executa a função
    removeBetslipCompletely();
    console.log('🏆 DEBUG: Script de remoção do betslip finalizado!');
});

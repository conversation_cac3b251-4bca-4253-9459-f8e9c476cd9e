<!DOCTYPE html>
<html lang="pt-br">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Copa do Mundo de Clubes 2025 | Casa de Apostas</title>

  <!-- Tailwind CSS Config -->
  <script src="https://cdn.tailwindcss.com">
  </script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'custom-dark': '#091D28'
          },
          fontFamily: {
            'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
          }
        },
        fontFamily: {
          'sans': ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif']
        }
      }
    }
  </script>

  <!-- Estilos CSS -->
  <style>
    /* Global Font Settings */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    body,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    span,
    a,
    button,
    input,
    select,
    textarea {
      font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }

    #copa-do-mundo-clubes-page {
      padding: 0;
    }

    #view-content>div {
      max-width: 100%;
    }

    body {
      font-family: 'Inter', ui-sans-serif, system-ui;
    }

    /* Scrollbar customization */
    .scrollbar-hide {
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;
      /* Firefox */
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
      /* Chrome, Safari, Opera */
    }

    /* Snap scrolling */
    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory>* {
      scroll-snap-align: center;
    }

    /* Hero Section Mobile-First */
    .hero-banner {
      width: 100%;
      height: auto;
      display: block;
      position: relative;
    }

    .hero-container {
      position: relative;
      width: 100%;
      overflow: hidden;
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    }

    .hero-image {
      width: 100%;
      height: 250px;
      object-fit: cover;
      object-position: center;
      transition: opacity 0.3s ease;
    }

    @media (min-width: 640px) {
      .hero-image {
        height: 350px;
      }
    }

    @media (min-width: 768px) {
      .hero-image {
        height: 400px;
      }
    }

    @media (min-width: 1024px) {
      .hero-image {
        height: 500px;
      }
    }

    @media (min-width: 1440px) {
      .hero-image {
        height: 600px;
      }
    }

    /* Overlay Content */
    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 1rem;
    }

    .hero-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: white;
      margin-bottom: 0.5rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .hero-subtitle {
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 1rem;
      max-width: 280px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    @media (min-width: 640px) {
      .hero-title {
        font-size: 2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
        max-width: 400px;
      }
    }

    @media (min-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.125rem;
        max-width: 500px;
      }
    }

    @media (min-width: 1024px) {
      .hero-title {
        font-size: 3rem;
      }

      .hero-subtitle {
        font-size: 1.25rem;
        max-width: 600px;
      }
    }

    /* CTA Button */
    .hero-cta {
      background: linear-gradient(45deg, #10b981, #34d399);
      color: white;
      font-weight: bold;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 2px solid transparent;
    }

    .hero-cta:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
      border-color: white;
    }

    @media (min-width: 640px) {
      .hero-cta {
        padding: 1rem 2rem;
        font-size: 1.125rem;
      }
    }

    /* Loading animations */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Scroll indicators */
    .scroll-indicator {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 12px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .drag-scroll:hover .scroll-indicator {
      opacity: 1;
    }

    /* Existing styles */
    .strikethrough {
      position: relative;
      font-weight: bold;
    }

    .strikethrough:before {
      position: absolute;
      content: "";
      left: 0;
      top: 50%;
      right: 0;
      border-top: 1.5px solid;
      border-color: #000;
      transform: rotate(-25deg);
      color: #000;
    }

    .slider-container {
      overflow: hidden;
      position: relative;
    }

    .slider {
      display: flex;
      backface-visibility: hidden;
      transform: translate3d(0, 0, 0);
    }

    #slider-copa {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
    }

    #slider-copa::-webkit-scrollbar {
      display: none;
    }

    #slider-copa>div {
      scroll-snap-align: start;
      flex-shrink: 0;
    }

    /* Enhanced scrollbar hiding and snap scroll for Odds cards */
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
      -webkit-overflow-scrolling: touch;
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }

    .snap-x {
      scroll-snap-type: x mandatory;
    }

    .snap-mandatory {
      scroll-snap-type: x mandatory;
    }

    .snap-start {
      scroll-snap-align: start;
    }

    /* Improved mobile card responsive behavior */
    .odds-card {
      scroll-snap-align: start;
      min-width: 280px;
    }

    @media (max-width: 640px) {
      .odds-card {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
      }
    }

    /* Countdown Timer Styles */
    .countdown-container {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      margin: 2rem 0;
    }

    .countdown-box {
      background: linear-gradient(135deg, #1e3a8a, #3b82f6);
      color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      text-align: center;
      min-width: 80px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .countdown-number {
      font-size: 1.5rem;
      font-weight: bold;
      display: block;
    }

    .countdown-label {
      font-size: 0.75rem;
      text-transform: uppercase;
      opacity: 0.8;
    }

    @media (min-width: 640px) {
      .countdown-box {
        min-width: 100px;
        padding: 1.5rem;
      }

      .countdown-number {
        font-size: 2rem;
      }

      .countdown-label {
        font-size: 0.875rem;
      }
    }

    /* Enhanced Animation for Banner */
    .animate-scroll {
      animation: scroll-left 80s linear infinite;
    }

    @keyframes scroll-left {
      0% {
        transform: translateX(0%);
      }

      100% {
        transform: translateX(-50%);
      }
    }

    /* Interactive hover effects */
    .fixture-card {
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .fixture-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .top-scorer-card {
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .top-scorer-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .top-scorer-card:hover::before {
      left: 100%;
    }

    .top-scorer-card:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    /* ODDS TURBINADAS SECTION CUSTOM BG */
    .odds-turbinadas-section {
      background: #000000 !important;
      background-color: #000000 !important;
      background-image: none !important;
    }

    /* RESPONSIVE BANNER IMAGE */
    .banner-image {
      width: 100%;
      height: auto;
      object-fit: cover;
      object-position: center center;
      display: block;
    }

    /* Mobile - Manter seção central visível */
    @media (max-width: 640px) {
      .banner-image {
        height: 250px;
        object-position: center 30%;
      }
    }

    /* Tablet - Ajustar posicionamento */
    @media (min-width: 641px) and (max-width: 1024px) {
      .banner-image {
        height: 350px;
        object-position: center 35%;
      }
    }

    /* Desktop - Mostrar imagem completa */
    @media (min-width: 1025px) {
      .banner-image {
        height: auto;
        object-position: center center;
      }
    }

    /* Telas muito pequenas - Foco na área central */
    @media (max-width: 480px) {
      .banner-image {
        height: 200px;
        object-position: center 25%;
      }
    }

    /* AJUSTE ESPECÍFICO PARA TEXTO EM TELAS < 720px */
    @media (max-width: 720px) {
      #copy-bannet-text {
        bottom: 0.25rem !important;
        /* Equivale a bottom-1 */
      }
    }

    /* Telas muito pequenas - posicionamento com top */
    @media (max-width: 480px) {
      #copy-bannet-text {
        top: 12rem !important;
        bottom: auto !important;
      }
    }
  </style>

  <!-- Drag-to-scroll para odds turbinadas -->
  <script>
    (function () {
      const oddsScroll = document.querySelector('.odds-scroll');
      let isDown = false;
      let startX;
      let scrollLeft;
      if (oddsScroll) {
        oddsScroll.addEventListener('mousedown', (e) => {
          isDown = true;
          oddsScroll.classList.add('active');
          startX = e.pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('mouseleave', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mouseup', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mousemove', (e) => {
          if (!isDown) return;
          e.preventDefault();
          const x = e.pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5; // scroll-fast
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
        // Touch support
        oddsScroll.addEventListener('touchstart', (e) => {
          isDown = true;
          startX = e.touches[0].pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('touchend', () => {
          isDown = false;
        });
        oddsScroll.addEventListener('touchmove', (e) => {
          if (!isDown) return;
          const x = e.touches[0].pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5;
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
      }
    })();
  </script> 

  <!-- Renderização dinâmica dos artilheiros consumindo artilheiros.json - Apenas Copa do Mundo de Clubes -->
  <script>
    // Função para aguardar o elemento estar disponível
    function waitForElement(selector, timeout = 10000) {
      return new Promise((resolve, reject) => {
        const element = document.getElementById(selector);
        if (element) {
          resolve(element);
          return;
        }

        const observer = new MutationObserver((mutations, obs) => {
          const element = document.getElementById(selector);
          if (element) {
            obs.disconnect();
            resolve(element);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        // Timeout para evitar espera indefinida
        setTimeout(() => {
          observer.disconnect();
          reject(new Error(`Elemento ${selector} não encontrado após ${timeout}ms`));
        }, timeout);
      });
    }

    // Aguardar o DOM estar carregado e depois aguardar o elemento
    document.addEventListener('DOMContentLoaded', async function () {
      // Verifica se está na página correta antes de executar
      if (!window.location.pathname.includes('/br/copa-do-mundo-clubes')) {
        console.log('⏭️ Script de artilheiros não executado - não está na página Copa do Mundo de Clubes');
        return;
      }

      try {
        console.log('Aguardando elemento artilheiros-copa...');
        const artilheirosDiv = await waitForElement('artilheiros-copa');
        console.log('Elemento artilheiros-copa encontrado!');

        // Mostrar spinner de carregamento
        artilheirosDiv.innerHTML = `
          <div class="flex justify-center items-center p-8" style="min-height: 200px;">
            <div class="loading-spinner"></div>
            <span class="ml-3 text-gray-400 font-semibold">Carregando artilheiros...</span>
          </div>
        `;

        try {
          const response = await fetch('https://cdn.jsdelivr.net/gh/mu-costa/artilheiros@master/artilheiros.json');
          if (!response.ok) {
            throw new Error(`Erro ao buscar o arquivo: ${response.status} ${response.statusText}`);
          }
          const artilheirosData = await response.json();

          artilheirosDiv.innerHTML = ''; // Limpar spinner

          if (!artilheirosData || artilheirosData.length === 0) {
            artilheirosDiv.innerHTML = '<p class="text-center text-gray-400 p-4">Nenhum artilheiro encontrado.</p>';
            return;
          }

          artilheirosData.forEach((artilheiro, idx) => {
            const foto = artilheiro['jogador-foto'] || 'https://via.placeholder.com/64x64?text=Foto';
            const escudo = artilheiro['jogador-escudo'] || 'https://via.placeholder.com/30x30?text=Escudo';
            const nome = artilheiro['jogador-nome'] || 'Nome não disponível';
            const posicao = artilheiro['jogador-posicao'] || 'Posição não informada';
            const gols = artilheiro['jogador-gols'] || '0';

            artilheirosDiv.insertAdjacentHTML('beforeend', `
              <div class="flex-shrink-0 w-40 sm:w-48 md:w-56 rounded-lg bg-gradient-to-b from-neutral-900 to-neutral-800 shadow-lg overflow-hidden flex flex-col items-center justify-between border border-amber-500/20 animate-fade-in-delay" style="animation-delay: ${idx * 100}ms">
                <div class="flex flex-col items-center justify-center bg-gradient-to-r from-amber-600 to-amber-500 p-4 w-full">
                  <div class="text-white text-2xl font-bold mb-2">#${idx + 1}</div>
                  <img src="${foto}" alt="${nome}" class="w-16 h-16 rounded-full border-2 border-white mb-2 object-cover shadow-md" onerror="this.onerror=null; this.src='https://via.placeholder.com/64x64?text=Foto'; this.alt='Foto indisponível';">
                  <div class="flex items-center gap-1 mb-2">
                    <img src="${escudo}" alt="Escudo" class="w-5 h-5 rounded-full" onerror="this.onerror=null; this.src='https://via.placeholder.com/20x20?text=E';">
                    <span class="block text-sm font-semibold text-white/80">${posicao}</span>
                  </div>
                  <span class="block text-2xl font-bold text-white">${gols} <span class='text-base font-normal'>gols</span></span>
                </div>
                <div class="p-3 w-full text-center">
                  <h4 class="text-base font-semibold text-amber-300">${nome}</h4>
                </div>
              </div>
            `);
          });
          console.log('Artilheiros renderizados com sucesso!');
        } catch (error) {
          console.error('Erro ao buscar ou processar dados do CDN:', error);
          artilheirosDiv.innerHTML = `
            <div class="p-4 bg-red-900/50 rounded-lg border border-red-700 my-4 text-center w-full">
              <p class="text-red-300 font-medium">Erro ao carregar os dados dos artilheiros.</p>
              <p class="text-gray-400 text-sm mt-1">Não foi possível buscar os dados do CDN.</p>
              <p class="text-gray-500 text-xs mt-2">${error.message}</p>
              <button onclick="location.reload()" class="mt-3 px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg text-sm transition-colors">
                Tentar novamente
              </button>
            </div>
          `;
        }
      } catch (error) {
        console.error('Erro ao aguardar elemento artilheiros-copa:', error);
      }
    });
  </script>

  <!-- ...Mock data ... -->
  <script>
    // Countdown Timer Function
    function startCountdown() {
      const targetDate = new Date('2025-06-18T16:00:00-03:00'); // Next game date

      function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate.getTime() - now;

        if (distance < 0) {
          document.getElementById('days').textContent = '00';
          document.getElementById('hours').textContent = '00';
          document.getElementById('minutes').textContent = '00';
          document.getElementById('seconds').textContent = '00';
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        document.getElementById('days').textContent = String(days).padStart(2, '0');
        document.getElementById('hours').textContent = String(hours).padStart(2, '0');
        document.getElementById('minutes').textContent = String(minutes).padStart(2, '0');
        document.getElementById('seconds').textContent = String(seconds).padStart(2, '0');
      }

      updateCountdown();
      setInterval(updateCountdown, 1000);
    }

    // Enhanced Banner Animation
    function initSliderMotion() {
      const slider = document.getElementById('slider-motion');
      if (!slider) return;

      // A animação agora é controlada principalmente pelo CSS
      // Isso garante que o slider seja inicializado corretamente
      const sliderContent = slider.querySelector('.animate-scroll');
      if (sliderContent) {
        // Garantir que a largura do conteúdo seja suficiente para cobrir toda a tela
        const viewportWidth = window.innerWidth;
        if (sliderContent.offsetWidth < viewportWidth * 2) {
          // Se o conteúdo não for largo o suficiente, ajustamos o CSS dinamicamente
          slider.style.minWidth = `${viewportWidth * 2}px`;
        }
      }
    }

    // Initialize drag scroll functionality
    function initDragScroll() {
      const scrollContainers = document.querySelectorAll('#slider-copa, #top-scorers-copa');
      scrollContainers.forEach(container => {
        if (!container) return;

        let isDown = false;
        let startX;
        let scrollLeft;

        container.addEventListener('mousedown', (e) => {
          isDown = true;
          container.style.cursor = 'grabbing';
          startX = e.pageX - container.offsetLeft;
          scrollLeft = container.scrollLeft;
        });

        container.addEventListener('mouseleave', () => {
          isDown = false;
          container.style.cursor = 'grab';
        });

        container.addEventListener('mouseup', () => {
          isDown = false;
          container.style.cursor = 'grab';
        });

        container.addEventListener('mousemove', (e) => {
          if (!isDown) return;
          e.preventDefault();
          const x = e.pageX - container.offsetLeft;
          const walk = (x - startX) * 2;
          container.scrollLeft = scrollLeft - walk;
        });      });
    }

    // Renderização dos artilheiros será tratada em outro script  
  </script>

  <!-- Render standings -->
  <script>
    
    const standingsTbody = document.getElementById('standings-copa');
    if (standingsTbody) {
      standings.forEach(team => {
        standingsTbody.insertAdjacentHTML('beforeend', `<tr><td class="px-2 py-1 text-black">${team.rank}</td><td class=" text-black px-2 py-1 flex items-center gap-2"><img src="${team.logo}" alt="${team.name}" class="text-black w-6 h-6 inline-block rounded-full border" />${team.name}</td><td class="text-black px-2 py-1">${team.points}</td><td class="text-black px-2 py-1">${team.played}</td><td class="text-black px-2 py-1">${team.win}</td><td class="text-black px-2 py-1">${team.draw}</td><td class="text-black px-2 py-1">${team.lose}</td></tr>`);
      });
    }

    // Inicializações
    document.addEventListener('DOMContentLoaded', () => {
      startCountdown();
      initSliderMotion();
      initDragScroll();
    });
  </script>
  
  <!-- Script de conexao com a API FOOTBALL DATA-->
  <script>
    const STANDINGS_API_URL = 'https://v3.football.api-sports.io/standings?league=15&season=2025';
    const API_KEY = '6ab9fabfb32d18cad9adb9525d1076ac'; // Substitua pela sua chave da API-SPORTS
    let standingsGroupsData = [];
    const CACHE_KEY = 'football_standings_cache';
    const CACHE_EXPIRY = 3600000; // 1 hora em milissegundos

    // Função para mostrar o estado de carregamento
    function showLoading(elementId) {
      const element = document.getElementById(elementId);
      if (element) {
        element.innerHTML = `
          <div class="flex justify-center items-center p-8">
            <div class="loading-spinner"></div>
            <span class="ml-3 text-gray-600 font-semibold">Carregando dados...</span>
          </div>
        `;
      }
    }

    // Função para obter dados do cache
    function getDataFromCache() {
      const cachedData = localStorage.getItem(CACHE_KEY);
      if (!cachedData) return null;

      try {
        const { data, timestamp } = JSON.parse(cachedData);
        // Verifica se o cache não expirou
        if (Date.now() - timestamp < CACHE_EXPIRY) {
          return data;
        }
      } catch (error) {
        console.error('Erro ao ler cache:', error);
      }

      return null;
    }

    // Função para salvar dados no cache
    function saveDataToCache(data) {
      try {
        const cacheData = {
          data: data,
          timestamp: Date.now()
        };
        localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      } catch (error) {
        console.error('Erro ao salvar cache:', error);
      }
    }    // Função principal para obter os dados de classificação
    async function getStandings(retryCount = 0) {
      const MAX_RETRIES = 3;
      const RETRY_DELAY = 2000; // 2 segundos

      // Verificar se o elemento existe antes de mostrar o carregamento
      const container = document.getElementById('standings-groups');
      if (!container) {
        console.error('Elemento de contêiner de classificação não encontrado');

        // Se estamos em uma tentativa de retry, tentar novamente após um delay
        if (retryCount < MAX_RETRIES) {
          console.log(`Aguardando DOM (${retryCount + 1}/${MAX_RETRIES})...`);
          setTimeout(() => getStandings(retryCount + 1), RETRY_DELAY);
        } else {
          console.error('Número máximo de tentativas atingido. O elemento standings-groups não foi encontrado.');
        }
        return;
      }

      // Mostrar estado de carregamento
      showLoading('standings-groups');

      try {
        // Primeiro tenta ler do cache
        const cachedData = getDataFromCache();
        if (cachedData) {
          console.log('Usando dados do cache');
          await processStandingsData(cachedData);
          return;
        }

        console.log('Buscando dados da API...');

        try {
          // Se não tiver cache, busca da API
          const response = await fetch(STANDINGS_API_URL, {
            method: 'GET',
            headers: {
              'x-apisports-key': API_KEY
            },
            // Adiciona timeout para evitar espera indefinida
            signal: AbortSignal.timeout(15000) // 15 segundos de timeout
          });

          if (response.ok) {
            const data = await response.json();

            // Validação mais rigorosa dos dados recebidos
            if (data && data.response &&
              Array.isArray(data.response) &&
              data.response[0] &&
              data.response[0].league &&
              data.response[0].league.standings) {

              console.log('Dados recebidos da API com sucesso');

              // Salvar no cache para uso futuro
              saveDataToCache(data.response[0].league.standings);

              // Processar e renderizar os dados
              await processStandingsData(data.response[0].league.standings);
            } else {
              throw new Error('Formato de dados inválido da API');
            }
          } else {
            throw new Error(`Erro na API: ${response.status} - ${response.statusText}`);
          }
        } catch (fetchError) {
          console.error('Erro ao buscar dados da API:', fetchError);

          // Se a API falhar, tentar novamente com backoff exponencial
          if (retryCount < MAX_RETRIES) {
            const delayTime = RETRY_DELAY * Math.pow(2, retryCount);
            console.log(`Tentando novamente em ${delayTime / 1000} segundos... (${retryCount + 1}/${MAX_RETRIES})`);

            // Mostra mensagem de nova tentativa
            container.innerHTML = `
              <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200 my-4">
                <p class="text-yellow-600 font-medium">Tentando novamente em ${delayTime / 1000} segundos...</p>
                <p class="text-gray-700 text-sm mt-2">Tentativa ${retryCount + 1} de ${MAX_RETRIES}</p>
                <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                  <div class="bg-yellow-400 h-2.5 rounded-full" style="width: ${((retryCount + 1) / MAX_RETRIES) * 100}%"></div>
                </div>
              </div>
            `;

            setTimeout(() => getStandings(retryCount + 1), delayTime);
          } else {
            // Se todas as tentativas falharem, mostrar erro final
            container.innerHTML = `
              <div class="p-4 bg-red-50 rounded-lg border border-red-200 my-4">
                <p class="text-red-600 font-medium">Erro ao buscar os dados da classificação!</p>
                <p class="text-gray-700 text-sm mt-2">${fetchError.message || 'Tente novamente mais tarde.'}</p>
                <button id="retry-standings" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                  Tentar novamente
                </button>
              </div>
            `;

            // Adicionar evento para tentar novamente
            const retryBtn = document.getElementById('retry-standings');
            if (retryBtn) {
              retryBtn.addEventListener('click', () => getStandings(0)); // Reinicia o contador de tentativas
            }
          }
        }
      } catch (error) {
        console.error('Erro geral ao buscar dados de classificação:', error);

        if (container) {
          container.innerHTML = `
            <div class="p-4 bg-red-50 rounded-lg border border-red-200 my-4">
              <p class="text-red-600 font-medium">Erro ao buscar os dados da classificação!</p>
              <p class="text-gray-700 text-sm mt-2">${error.message || 'Tente novamente mais tarde.'}</p>
              <button id="retry-standings" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                Tentar novamente
              </button>
            </div>
          `;

          // Adicionar evento para tentar novamente
          const retryBtn = document.getElementById('retry-standings');
          if (retryBtn) {
            retryBtn.addEventListener('click', () => getStandings(0));
          }
        }
      }
    }// Função para processar os dados recebidos da API ou cache
    async function processStandingsData(data) {
      return new Promise((resolve) => {
        // Validação de dados antes de processamento
        if (!data || !Array.isArray(data) || data.length === 0) {
          console.error('Dados de classificação inválidos:', data);
          throw new Error('Dados de classificação inválidos ou vazios');
        }

        // Garante que todos os dados esperados estão presentes
        const validData = data.filter(group =>
          Array.isArray(group) && group.length > 0 &&
          group.every(team =>
            team && team.rank && team.team && team.team.name &&
            team.points !== undefined && team.all && team.all.played !== undefined
          )
        );

        if (validData.length === 0) {
          console.error('Nenhum dado válido de classificação encontrado');
          throw new Error('Dados de classificação em formato inválido');
        }

        // Armazena os dados válidos para uso posterior
        standingsGroupsData = validData;

        // Verificar se o DOM está pronto e renderizar os dados
        function checkDOMAndRender() {
          // Verificar se os elementos existem antes de renderizar
          const select = document.getElementById('group-select');
          const container = document.getElementById('standings-groups');

          if (!select || !container) {
            // Se os elementos ainda não existirem, aguardar e tentar novamente
            console.log('Elementos DOM para classificação ainda não disponíveis, aguardando...');
            setTimeout(checkDOMAndRender, 200);
            return;
          }

          console.log('Elementos DOM encontrados, renderizando dados...');

          // Limpar conteúdo anterior e mostrar estado de carregamento
          container.innerHTML = `
            <div class="flex justify-center items-center p-4">
              <div class="loading-spinner"></div>
              <span class="ml-3 text-gray-600 font-semibold">Preparando dados...</span>
            </div>
          `;

          // Usar requestAnimationFrame para garantir que a renderização ocorra no próximo frame
          requestAnimationFrame(() => {
            try {
              // Renderizar os dados
              renderGroupSelect(standingsGroupsData);
              renderStandingsGroup(0); // Mostra o primeiro grupo por padrão

              // Confirma visualmente que os dados foram carregados
              container.classList.add('fade-in');

              console.log('Dados renderizados com sucesso.');
              resolve();
            } catch (error) {
              console.error('Erro ao renderizar dados:', error);
              container.innerHTML = `
                <div class="p-4 bg-red-50 rounded-lg border border-red-200 my-4">
                  <p class="text-red-600 font-medium">Erro ao renderizar os dados da classificação!</p>
                  <p class="text-gray-700 text-sm mt-2">${error.message || 'Erro desconhecido'}</p>
                  <button id="retry-render" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                    Tentar novamente
                  </button>
                </div>
              `;

              // Adicionar evento para tentar novamente
              const retryBtn = document.getElementById('retry-render');
              if (retryBtn) {
                retryBtn.addEventListener('click', () => checkDOMAndRender());
              }

              resolve();
            }
          });
        }

        // Iniciar o processo de verificação do DOM e renderização
        checkDOMAndRender();
      });
    } function renderGroupSelect(groups) {
      const select = document.getElementById('group-select');
      if (!select) {
        console.error('Elemento de seleção de grupo não encontrado');
        return;
      }

      // Limpa o conteúdo atual
      select.innerHTML = '';

      // Validação de dados
      if (!Array.isArray(groups) || groups.length === 0) {
        console.error('Dados de grupos inválidos');
        select.innerHTML = '<option value="-1">Nenhum grupo disponível</option>';
        return;
      }

      // Adiciona as opções de grupo
      groups.forEach((groupArr, idx) => {
        if (!Array.isArray(groupArr) || groupArr.length === 0) return;

        try {
          const groupName = groupArr[0].group || `Grupo ${String.fromCharCode(65 + idx)}`;
          const option = document.createElement('option');
          option.value = idx;
          option.textContent = groupName;
          select.appendChild(option);
        } catch (error) {
          console.error(`Erro ao processar grupo ${idx}:`, error);
        }
      });

      // Se nenhuma opção foi adicionada
      if (select.options.length === 0) {
        select.innerHTML = '<option value="-1">Nenhum grupo disponível</option>';
        return;
      }

      // Adiciona o manipulador de eventos
      select.onchange = function () {
        const groupIndex = Number(this.value);
        if (groupIndex >= 0) {
          renderStandingsGroup(groupIndex);
        }
      };
    } function renderStandingsGroup(idx) {
      const container = document.getElementById('standings-groups');
      if (!container) {
        console.error('Elemento de contêiner de classificação não encontrado');
        return;
      }

      // Limpa o conteúdo atual
      container.innerHTML = '';

      // Validação dos dados
      if (!standingsGroupsData || !Array.isArray(standingsGroupsData) ||
        !standingsGroupsData[idx] || !Array.isArray(standingsGroupsData[idx]) ||
        standingsGroupsData[idx].length === 0) {
        container.innerHTML = `
          <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200 my-4">
            <p class="text-yellow-600 font-medium">Nenhum dado disponível para este grupo.</p>
          </div>
        `;
        return;
      }

      const groupArr = standingsGroupsData[idx];
      const groupName = groupArr[0] && groupArr[0].group ?
        groupArr[0].group :
        `Grupo ${String.fromCharCode(65 + idx)}`;

      // Criação da tabela com tratamento de erro para campos ausentes
      let table = `<div class='overflow-x-auto rounded-lg border border-gray-200 bg-white shadow mt-4'>
        <h3 class='text-lg font-bold text-green-700 mb-2 px-4 pt-4'>${groupName}</h3>
        <table class='min-w-[600px] w-full bg-white rounded-lg'>
          <thead class='bg-gradient-to-r from-gray-50 to-gray-100'>
            <tr>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pos</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider text-left'>Time</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>Pts</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>J</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>V</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>E</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>D</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GP</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>GC</th>
              <th class='px-3 py-2 text-xs font-bold text-gray-700 uppercase tracking-wider'>SG</th>
            </tr>
          </thead>
          <tbody class='bg-white divide-y divide-gray-200'>`;

      // Processamento seguro de cada equipe
      groupArr.forEach(team => {
        try {
          // Verifica e fornece valores padrão para campos que podem estar ausentes
          const rank = team.rank || '-';
          const teamName = team.team && team.team.name ? team.team.name : 'Nome não disponível';
          const teamLogo = team.team && team.team.logo ? team.team.logo : 'https://placehold.co/30x30?text=?';
          const points = team.points !== undefined ? team.points : '-';

          // Valores padrão para estatísticas ausentes
          const played = team.all && team.all.played !== undefined ? team.all.played : '-';
          const win = team.all && team.all.win !== undefined ? team.all.win : '-';
          const draw = team.all && team.all.draw !== undefined ? team.all.draw : '-';
          const lose = team.all && team.all.lose !== undefined ? team.all.lose : '-';
          const goalsFor = team.all && team.all.goals && team.all.goals.for !== undefined ? team.all.goals.for : '-';
          const goalsAgainst = team.all && team.all.goals && team.all.goals.against !== undefined ? team.all.goals.against : '-';
          const goalsDiff = team.goalsDiff !== undefined ? team.goalsDiff : '-';

          // Adiciona a linha da equipe com tratamento de erro para imagem
          table += `<tr class='hover:bg-green-50 transition'>
            <td class='px-2 py-1 text-black text-center font-semibold'>${rank}</td>
            <td class='px-2 py-1 text-black flex items-center gap-2'>
              <img src='${teamLogo}' alt='${teamName}' class='w-6 h-6 inline-block rounded-full border' 
                   onerror="this.onerror=null; this.src='https://placehold.co/30x30?text=?';" />${teamName}
            </td>
            <td class='px-2 py-1 text-black text-center font-bold text-green-700'>${points}</td>
            <td class='px-2 py-1 text-black text-center'>${played}</td>
            <td class='px-2 py-1 text-black text-center'>${win}</td>
            <td class='px-2 py-1 text-black text-center'>${draw}</td>
            <td class='px-2 py-1 text-black text-center'>${lose}</td>
            <td class='px-2 py-1 text-black text-center'>${goalsFor}</td>
            <td class='px-2 py-1 text-black text-center'>${goalsAgainst}</td>
            <td class='px-2 py-1 text-black text-center'>${goalsDiff}</td>
          </tr>`;
        } catch (error) {
          console.error('Erro ao processar equipe:', error, team);
          // Linha de erro para equipe com dados incompletos
          table += `<tr class='bg-red-50'>
            <td colspan="10" class='px-2 py-1 text-center text-red-600'>Erro ao processar dados da equipe</td>
          </tr>`;
        }
      });

      table += '</tbody></table></div>';

      try {
        container.insertAdjacentHTML('beforeend', table);
      } catch (error) {
        console.error('Erro ao inserir tabela no DOM:', error);
        container.innerHTML = `
          <div class="p-4 bg-red-50 rounded-lg border border-red-200 my-4">
            <p class="text-red-600 font-medium">Erro ao renderizar tabela de classificação.</p>
            <p class="text-gray-700 text-sm mt-2">${error.message || 'Erro desconhecido'}</p>
          </div>
        `;
      }
    }    // Inicializa a busca de dados somente após o DOM estar carregado e na página correta
    document.addEventListener('DOMContentLoaded', () => {
      // Verifica se está na página correta antes de buscar dados
      if (!window.location.pathname.includes('/br/copa-do-mundo-clubes')) {
        console.log('⏭️ Script de standings não executado - não está na página Copa do Mundo de Clubes');
        return;
      }

      console.log('DOM carregado, iniciando busca de dados na página Copa do Mundo de Clubes...');

      // Pequeno atraso para garantir que todos os elementos estejam disponíveis
      setTimeout(() => {
        getStandings();
      }, 500);
    });
  </script>

  <!-- Drag-to-scroll para odds turbinadas -->
  <script>

    (function () {
      const oddsScroll = document.querySelector('.odds-scroll');
      let isDown = false;
      let startX;
      let scrollLeft;
      if (oddsScroll) {
        oddsScroll.addEventListener('mousedown', (e) => {
          isDown = true;
          oddsScroll.classList.add('active');
          startX = e.pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('mouseleave', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mouseup', () => {
          isDown = false;
          oddsScroll.classList.remove('active');
        });
        oddsScroll.addEventListener('mousemove', (e) => {
          if (!isDown) return;
          e.preventDefault();
          const x = e.pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5; // scroll-fast
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
        // Touch support
        oddsScroll.addEventListener('touchstart', (e) => {
          isDown = true;
          startX = e.touches[0].pageX - oddsScroll.offsetLeft;
          scrollLeft = oddsScroll.scrollLeft;
        });
        oddsScroll.addEventListener('touchend', () => {
          isDown = false;
        });
        oddsScroll.addEventListener('touchmove', (e) => {
          if (!isDown) return;
          const x = e.touches[0].pageX - oddsScroll.offsetLeft;
          const walk = (x - startX) * 1.5;
          oddsScroll.scrollLeft = scrollLeft - walk;
        });
      }
    })();
  </script>

  <!-- Betslip remover - Apenas para página Copa do Mundo de Clubes --->
  <script>
    // Verifica se está na página correta antes de executar
    function isCopaDoMundoPage() {
      return window.location.pathname.includes('/br/copa-do-mundo-clubes');
    }

    document.addEventListener("DOMContentLoaded", () => {
      // Só executa se estiver na página correta
      if (!isCopaDoMundoPage()) {
        console.log('⏭️ Script de betslip não executado - não está na página Copa do Mundo de Clubes');
        return;
      }

      console.log('✅ Executando script de betslip - página Copa do Mundo de Clubes detectada');
      
      const observer = new MutationObserver(() => {
        const containerRight = document.querySelector(".view-widget-container-right");
        const betslipDesktop = document.querySelector(".betslip-desktop");

        if (containerRight && betslipDesktop) {
          // Move o betslip para o body ANTES de esconder o container lateral
          document.body.appendChild(betslipDesktop);

          // Esconde o container lateral (espaço em branco)
          containerRight.style.display = "none";

          // Ajusta o estilo do betslip
          betslipDesktop.style.position = "fixed";
          betslipDesktop.style.bottom = "0";
          betslipDesktop.style.right = "0";
          betslipDesktop.style.zIndex = "9999";
          betslipDesktop.style.maxHeight = "90vh";
          betslipDesktop.style.overflowY = "auto";
          betslipDesktop.style.display = "block";
          betslipDesktop.style.background = "white"; // opcional, se estiver transparente
          betslipDesktop.style.boxShadow = "0 0 10px rgba(0,0,0,0.3)"; // opcional

          observer.disconnect();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    });


  </script>

  <!-- Betslip reorganizer - Apenas para página Copa do Mundo de Clubes -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Verifica se está na página correta antes de executar
      if (!window.location.pathname.includes('/br/copa-do-mundo-clubes')) {
        console.log('⏭️ Script de reorganização betslip não executado - não está na página Copa do Mundo de Clubes');
        return;
      }

      console.log('🚀 DEBUG: DOM carregado, iniciando reorganização do betslip na página Copa do Mundo de Clubes...');

      // Função para reorganizar o container do betslip
      function reorganizeBetslipContainer() {
        console.log('📋 DEBUG: Iniciando reorganizeBetslipContainer()');

        // Captura os elementos necessários
        const mainContainer = document.querySelector('.view-widget-container-main');
        const rightContainer = document.querySelector('.view-widget-container-right');
        const betslip = document.querySelector('.betslip-desktop');

        console.log('🔍 DEBUG: Elementos encontrados:');
        console.log('  - mainContainer:', mainContainer ? '✅ Encontrado' : '❌ Não encontrado');
        console.log('  - rightContainer:', rightContainer ? '✅ Encontrado' : '❌ Não encontrado');
        console.log('  - betslip:', betslip ? '✅ Encontrado' : '❌ Não encontrado');

        if (mainContainer && rightContainer && betslip) {
          console.log('✨ DEBUG: Todos os elementos encontrados, procedendo com a reorganização...');

          // Remove o container direito da sua posição atual
          rightContainer.remove();
          console.log('🗑️ DEBUG: Container direito removido');

          // Modifica o estilo do container principal para não considerar o espaço do widget direito
          mainContainer.style.width = '100%';
          mainContainer.classList.remove('xl:w-[calc(100%-var(--widget-container-right-width))]');
          console.log('📐 DEBUG: Largura do container principal alterada para 100%');

          // Cria um novo container sticky dentro do container principal
          const stickyContainer = document.createElement('div');
          stickyContainer.className = 'betslip-sticky-container';
          stickyContainer.style.cssText = `
                           position: sticky;
                           top: 20px;
                           right: 20px;
                           width: 0;
                           height: 0;
                           overflow: visible;
                           z-index: 1000;
                           pointer-events: none;
                           float: right;
                           margin-top: 20px;
                           margin-right: 20px;
                       `;
          console.log('📦 DEBUG: Container sticky criado');

          // Modifica o betslip para ficar flutuante
          betslip.style.cssText = `
                           position: fixed;
                           top: auto;
                           bottom: 20px;
                           right: 20px;
                           width: 384px;
                           margin-left: 0;
                           z-index: 1000;
                           box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                           pointer-events: auto;
                           border-radius: 8px;
                           overflow: hidden;
                       `;
          console.log('🎨 DEBUG: Estilos do betslip aplicados');

          // Adiciona o betslip ao novo container sticky
          stickyContainer.appendChild(betslip);
          console.log('🔗 DEBUG: Betslip adicionado ao container sticky');

          // Adiciona o container sticky ao container principal
          mainContainer.appendChild(stickyContainer);
          console.log('🔗 DEBUG: Container sticky adicionado ao container principal');

          // Melhora o estilo do betslip interno
          const betslipInner = betslip.querySelector('.betslip-inner');
          if (betslipInner) {
            betslipInner.style.borderRadius = '8px';
            betslipInner.style.maxHeight = '80vh';
            console.log('✅ DEBUG: Estilos do betslip interno aplicados');
          } else {
            console.log('⚠️ DEBUG: betslip-inner não encontrado');
          }

          console.log('🎉 DEBUG: Betslip reorganizado com sucesso!');
        } else {
          console.log('❌ DEBUG: Falha na reorganização - elementos não encontrados');
          if (!mainContainer) console.log('  ❌ mainContainer não encontrado');
          if (!rightContainer) console.log('  ❌ rightContainer não encontrado');
          if (!betslip) console.log('  ❌ betslip não encontrado');
        }
      }

      // Função para adicionar funcionalidade de minimizar/maximizar
      function setupBetslipToggle() {
        console.log('🔄 DEBUG: Iniciando setupBetslipToggle()');

        const betslipToggleBtn = document.querySelector('.betslip-header-inner-close');
        const betslipContent = document.querySelector('.betslip-inner-content-wrapper');

        console.log('🔍 DEBUG: Elementos de toggle encontrados:');
        console.log('  - betslipToggleBtn:', betslipToggleBtn ? '✅ Encontrado' : '❌ Não encontrado');
        console.log('  - betslipContent:', betslipContent ? '✅ Encontrado' : '❌ Não encontrado');

        if (betslipToggleBtn && betslipContent) {
          betslipToggleBtn.addEventListener('click', function () {
            console.log('🖱️ DEBUG: Botão de toggle clicado');
            if (betslipContent.style.display === 'none') {
              betslipContent.style.display = 'flex';
              const icon = betslipToggleBtn.querySelector('svg');
              if (icon) icon.style.transform = 'rotate(0deg)';
              console.log('📖 DEBUG: Betslip expandido');
            } else {
              betslipContent.style.display = 'none';
              const icon = betslipToggleBtn.querySelector('svg');
              if (icon) icon.style.transform = 'rotate(180deg)';
              console.log('📕 DEBUG: Betslip minimizado');
            }
          });
          console.log('✅ DEBUG: Event listener do botão de toggle adicionado');
        }

        // Permite clicar no cabeçalho para minimizar/maximizar
        const betslipHeader = document.querySelector('.betslip-header-inner-center');
        console.log('🔍 DEBUG: betslipHeader:', betslipHeader ? '✅ Encontrado' : '❌ Não encontrado');

        if (betslipHeader && betslipContent) {
          betslipHeader.style.cursor = 'pointer';
          betslipHeader.addEventListener('click', function () {
            console.log('🖱️ DEBUG: Cabeçalho do betslip clicado');
            if (betslipContent.style.display === 'none') {
              betslipContent.style.display = 'flex';
              const icon = document.querySelector('.betslip-header-inner-close svg');
              if (icon) icon.style.transform = 'rotate(0deg)';
              console.log('📖 DEBUG: Betslip expandido via cabeçalho');
            } else {
              betslipContent.style.display = 'none';
              const icon = document.querySelector('.betslip-header-inner-close svg');
              if (icon) icon.style.transform = 'rotate(180deg)';
              console.log('📕 DEBUG: Betslip minimizado via cabeçalho');
            }
          });
          console.log('✅ DEBUG: Event listener do cabeçalho adicionado');
        }

        console.log('🎉 DEBUG: setupBetslipToggle() concluído!');
      }

      // Executa as funções após o DOM estar carregado
      console.log('🏁 DEBUG: Executando funções principais...');
      reorganizeBetslipContainer();
      setupBetslipToggle();
      console.log('🏆 DEBUG: Script de reorganização do betslip finalizado!');
    });
  </script>

</head>

<body class="bg-gray-100 min-h-screen">
  <!-- Banner and Hero section -->
  <div class="w-full relative">
    <img src="https://staging-minio.devcore.at/next-cms/HJRzESNqHeRLhFeIYxV6uA.png" sizes="100vw" class="banner-image"
      alt="Copa do Mundo de Clubes 2025 - Banner Principal" loading="eager">
    <div id="copy-bannet-text"
      class="absolute bottom-2 sm:bottom-4 md:bottom-5 lg:bottom-6 xl:bottom-1 left-0 right-0 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20">
      <div class="flex flex-col justify-start items-start gap-1 sm:gap-2 w-full max-w-4xl">
        <div
          class="text-amber-300 text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-extrabold font-['Inter'] uppercase leading-tight">
          Copa do Mundo de Clubes da fifa</div>
        <div
          class="text-white text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium font-['Inter'] leading-relaxed max-w-3xl lg:max-w-none">
          Acompanhe tudo do mundial e aproveite as melhores Odds na Casa!</div>
      </div>
    </div>
  </div>

  <!-- Odds Turbinadas Section -->
  <div class="w-full px-4 sm:px-6 md:px-20 pt-24 pb-14 bg-black">
    <div class="max-w-7xl mx-auto">
      <div class="flex flex-col gap-8">
        <div class="flex flex-col gap-4">
          <!-- Special Markets -->
          <div class="self-stretch pr-4 inline-flex flex-col justify-start items-start gap-3">
            <h2 class="text-[#E9C043] text-2xl font-semibold">Mercados especiais</h2>
            <p class="text-white text-base"> As melhores odds para você sentir a emoção do mundial a cada lance.</p>

            <div class="flex items-center gap-2">
              <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                    fill="white" />
                </svg>
                <p style="padding-left: 0.5rem;">Deslize para o lado e explore mais mercados. </p>
              </div>

            </div>
            <div class="w-full h-full rounded-lg" style="border-radius: 100px">
              <cms-widget-ref widget-id="4836" widget-identifier="copa-do-mundo-clubes-hb"></cms-widget-ref>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>

  <!-- Animated Banner Section -->
  <div class="w-full bg-[#E9C043] py-4 overflow-hidden">
    <div id="slider-motion" class="flex whitespace-nowrap">
      <div
        class="flex items-center justify-start text-[#070300] text-base font-bold font-sans uppercase leading-relaxed whitespace-nowrap animate-scroll">
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <!-- Duplicação do conteúdo para preencher todo o espaço -->
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
      </div>
    </div>
  </div>

  <!-- Standings Group Selector and Table -->
  <div id="standings-section" class="w-full max-w-3xl mx-auto mt-8 mb-8 px-4">
    <div class="flex flex-col gap-4">

      <div class="self-stretch px-4 inline-flex flex-col justify-start items-start gap-4">
        <div class="self-stretch pt-2 pb-3 border-b border-black inline-flex justify-center items-center gap-10">
          <div
            class="w-6 h-6 origin-top-left rotate-180 inline-flex flex-col justify-center items-center overflow-hidden">
          </div>
          <div class="flex-1 h-4 text-center justify-center text-sm font-semibold font-sans">FASE DE GRUPOS
          </div>
          <div class="w-6 h-6 inline-flex flex-col justify-center items-center overflow-hidden">
          </div>
        </div>
        <div class="self-stretch text-center justify-center  text-sm font-normal font-sans">32 times divididos
          em 8 grupos de 4. Os dois melhores de cada grupo avançam para a próxima fase.</div>
        <div class="self-stretch inline-flex justify-center items-center gap-2">
          <div class="flex-1 max-w-5 flex justify-end items-center gap-2">
          </div>
          <div class="self-stretch inline-flex justify-center  text-sm font-normal font-sans">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
                fill="black" />
            </svg>
            <p style="padding-left: 0.5rem;">Toque e selecione um grupo</p>
          </div>
        </div>
      </div>

      <!-- Group Selector -->
      <div
        class="flex flex-col sm:flex-row items-center justify-between gap-4 bg-white rounded-lg shadow p-4 border border-gray-200">
        <label for="group-select" class="text-black text-base font-semibold font-sans">Selecione um grupo:</label>
        <select id="group-select"
          class="w-full sm:w-60 px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-700 text-black font-medium bg-gray-50 transition">
        </select>
      </div>
      <div id="standings-groups"></div>
    </div>
  </div>

  <!-- Animated Banner Section -->
  <div class="w-full bg-[#E9C043] py-4 overflow-hidden">
    <div id="slider-motion" class="flex whitespace-nowrap">
      <div
        class="flex items-center justify-start text-[#070300] text-base font-bold font-sans uppercase leading-relaxed whitespace-nowrap animate-scroll">
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <!-- Duplicação do conteúdo para preencher todo o espaço -->
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">APOSTE EM CADA LANCE</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">CADASTRE-SE AGORA</span>
        <div class="w-2 h-2 mx-4"></div>
        <span class="text-[#070300]">*</span>
        <div class="w-2 h-2 mx-4"></div>
      </div>
    </div>
  </div>

  <!-- Topscore rankings-->
  <div class="self-stretch py-14 bg-gradient-to-b from-stone-900 to-stone-950 flex flex-col gap-8">
    <div class="self-stretch px-4 flex flex-col gap-3">
      <div class="text-amber-300 text-3xl font-extrabold font-sans text-center">Artilheiros da Copa</div>
      <div class="self-stretch justify-center text-white text-base font-normal font-sans text-center">
        Quem será o goleador do Mundial? Acompanhe a corrida pela artilharia e as maiores promessas do torneio.
      </div>
      <div class="flex justify-center items-center gap-1">
        <div class="self-stretch inline-flex justify-center text-white text-sm font-normal font-sans">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M19.2162 13.1769C18.9234 13.1683 18.7137 12.9422 18.7055 12.6411C18.6134 8.99971 16.7384 6.30436 13.8923 5.24971L11.9423 10.6069C11.9255 10.6486 11.9337 10.6822 11.9755 10.699C12.0091 10.7158 12.0341 10.699 12.0595 10.674L13.047 9.60257C13.6916 8.90757 14.512 8.85757 15.148 9.39329C15.868 10.004 15.8766 10.9165 15.173 11.779L12.3691 15.169C10.2512 17.7304 7.76552 18.5254 4.98624 17.5126C1.66302 16.3072 0.248381 13.2604 1.58802 9.57721L2.21552 7.8615C2.86838 6.04507 4.11552 5.30007 5.58052 5.81079C5.9741 5.30007 6.57695 5.12436 7.22945 5.35864C7.46411 5.44836 7.68413 5.57246 7.88231 5.72686C8.30945 5.17436 8.97052 4.97329 9.65731 5.21614C9.84458 5.28786 10.0218 5.38354 10.1845 5.50079L11.1387 2.88936C11.4984 1.89293 12.4027 1.47471 13.3316 1.80936C14.2691 2.15257 14.6877 3.04864 14.328 4.04436L14.2609 4.22864C17.5002 5.44221 19.752 8.56436 19.752 12.6244C19.752 12.9254 19.5009 13.1851 19.2162 13.1769ZM5.34624 16.4161C7.5391 17.2197 9.59838 16.8094 11.532 14.474L14.3362 11.1008C14.6373 10.7494 14.6373 10.4061 14.3695 10.1715C14.1184 9.94579 13.7752 10.0211 13.4991 10.3054L11.5655 12.3061C11.2391 12.6411 10.9545 12.6744 10.678 12.574C10.3516 12.4569 10.2095 12.1054 10.3434 11.7454L13.3066 3.59257C13.432 3.25757 13.2816 2.93971 12.9634 2.82221C12.637 2.70507 12.3355 2.86436 12.2098 3.199L10.092 9.01686C9.99195 9.29293 9.68195 9.41829 9.40588 9.31793C9.13802 9.21757 8.99588 8.92436 9.09624 8.6565L9.85802 6.55579C9.74052 6.44686 9.5816 6.33793 9.42231 6.27936C9.02945 6.13721 8.70267 6.31293 8.55195 6.72293L7.88231 8.55614C7.78231 8.84079 7.47231 8.94971 7.20445 8.849C6.94481 8.75686 6.77767 8.48114 6.8866 8.18793L7.43052 6.70614C7.30664 6.58219 7.1584 6.48527 6.99517 6.4215C6.60159 6.27936 6.27517 6.45507 6.12445 6.86543L5.68088 8.08757C5.57231 8.38043 5.26231 8.48936 4.99445 8.38864C4.93 8.36636 4.87059 8.33153 4.81967 8.28617C4.76875 8.24081 4.72732 8.18581 4.69777 8.12434C4.66821 8.06288 4.65113 7.99618 4.64749 7.92807C4.64386 7.85997 4.65375 7.79183 4.6766 7.72757L5.01124 6.82329C4.30838 6.57221 3.64695 7.1415 3.19517 8.39721L2.66767 9.82864C1.52088 12.9926 2.56731 15.4033 5.34624 16.4161Z"
              fill="white" />
          </svg>
          <p style="padding-left: 0.5rem;">Deslize para o lado e confira o ranking!</p>
        </div>
      </div>
    </div>
    <div
      class="self-stretch h-10 py-2.5 border-t border-b border-amber-500/30 inline-flex justify-start items-center gap-2 px-4">
      <div class="flex-1 justify-center text-amber-300 text-base font-semibold font-sans uppercase leading-tight">
        ranking</div>
      <div class="text-right justify-center text-amber-300 text-base font-semibold font-sans uppercase leading-tight">
        gols</div>
    </div>
    <div class="w-full px-4 flex flex-col items-center">
      <div id="artilheiros-copa" class="w-full flex gap-4 overflow-x-auto scrollbar-hide pb-4 snap-x snap-mandatory">
      </div>
    </div>
  </div>

  <!-- Responsibility Game -->
  <div class="self-stretch py-14 bg-gray-100 flex flex-col gap-8">
    <div class="self-stretch px-4 flex flex-col gap-3">
      <div class="text-slate-900 text-3xl font-extrabold font-sans text-center">Jogue com responsabilidade!</div>
      <div class="self-stretch justify-center text-slate-700 text-base font-normal font-sans text-center">
        A emoção do mundial começa aqui! Acompanhe todos os jogos, confira estatísticas e aproveite odds turbinadas,
        tudo em um só lugar!
      </div>
      <div class="flex justify-center items-center gap-1">
        <div class="self-stretch inline-flex justify-center text-slate-700 text-sm font-normal font-sans"></div>
        <p class="self-stretch justify-center text-slate-700 text-base font-normal font-sans text-center"
          style="padding-left: 0.5rem;">Leia todas as informações disponíveis na nossa seção de <span
            class="text-emerald-700 font-semibold underline">
            <a href="https://casadeapostas.bet.br/br/jogo-responsavel">Jogo Responsável</a></span> e dos nos nossos
          <span class="text-emerald-700 font-semibold underline">
            <a href="https://casadeapostas.bet.br/br/terms-and-conditions"> Termos e Condições</a></span>.</p>
      </div>
    </div>
  </div>
  
  <div class="w-full px-4 flex flex-col items-center">
    <div class="w-full max-w-4xl">
    </div>
  </div>
  </div>

</body>

</html>
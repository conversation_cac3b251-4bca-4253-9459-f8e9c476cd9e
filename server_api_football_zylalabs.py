from flask import Flask, jsonify, request
import http.client
import json
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # Permite requisições do frontend

API_URL = '/api/6508/fifa+club+world+cup+data+api/9416/obtain+fifa+club+world+cup+match+fixtures+by+group'
API_HOST = 'zylalabs.com'
API_AUTH = 'Bearer 8519|GytIlTLjDCHjua42q0U02NpW7NJqCXZ9K1aP4NUI'

@app.route('/fixtures', methods=['POST'])
def get_fixtures():
    # Espera receber o nome do grupo via JSON do frontend
    req_data = request.get_json()
    group = req_data.get('group', 'Required')
    
    conn = http.client.HTTPSConnection(API_HOST)
    headers = {
        'Authorization': API_AUTH,
        'Content-Type': 'application/json'
    }
    body = json.dumps({"group": group})
    conn.request("POST", API_URL, body=body, headers=headers)
    res = conn.getresponse()
    data = res.read()
    if res.status == 200:
        return data, 200, {'Content-Type': 'application/json'}
    else:
        return jsonify({'error': 'Error fetching data', 'status': res.status}), res.status

if __name__ == '__main__':
    app.run(debug=True)
